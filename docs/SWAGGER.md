# Documentación Swagger/OpenAPI

## Descripción

Este proyecto incluye documentación interactiva de la API utilizando Swagger/OpenAPI 3.0 a través de la biblioteca `springdoc-openapi`. La documentación proporciona una interfaz web interactiva para explorar y probar todos los endpoints de la API.

## Acceso a la Documentación

### URLs de Acceso

Una vez que la aplicación esté ejecutándose, puedes acceder a la documentación en las siguientes URLs:

- **Swagger UI (Interfaz Interactiva)**: http://localhost:8080/swagger-ui.html
- **Especificación OpenAPI (JSON)**: http://localhost:8080/v3/api-docs
- **Especificación OpenAPI (YAML)**: http://localhost:8080/v3/api-docs.yaml

### Características de la Documentación

- **Interfaz Interactiva**: Permite probar endpoints directamente desde el navegador
- **Autenticación JWT**: Soporte completo para autenticación Bearer Token
- **Ejemplos de Respuesta**: Incluye ejemplos detallados de requests y responses
- **Agrupación por Tags**: Endpoints organizados por funcionalidad
- **Filtros y Búsqueda**: Capacidad de filtrar y buscar endpoints específicos

## Configuración

### Dependencia Maven

```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.5.0</version>
</dependency>
```

### Configuración de Propiedades

Las siguientes propiedades están configuradas en `application.properties`:

```properties
# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.displayRequestDuration=true
springdoc.swagger-ui.defaultModelsExpandDepth=1
springdoc.swagger-ui.defaultModelExpandDepth=1
```

## Uso de la Documentación

### 1. Autenticación

Para probar endpoints protegidos:

1. Navega a http://localhost:8080/swagger-ui.html
2. Haz clic en el botón "Authorize" en la parte superior derecha
3. Ingresa tu token JWT en el formato: `Bearer {tu-token-aqui}`
4. Haz clic en "Authorize"

### 2. Probando Endpoints

1. **Login**: Primero usa el endpoint `POST /auth/login` para obtener un token
2. **Autorización**: Usa el token obtenido para autorizar las siguientes requests
3. **Exploración**: Navega por los diferentes tags para explorar la funcionalidad

### 3. Tags Disponibles

- **Autenticación**: Login, logout, refresh tokens, perfil de usuario
- **Administración**: Gestión de usuarios (requiere rol ADMIN)
- **Push Tokens**: Gestión de tokens de notificaciones push (solo móvil)

## Estructura de la API

### Endpoints Públicos
- `POST /auth/login` - Iniciar sesión
- `POST /auth/refresh` - Renovar tokens

### Endpoints Protegidos
- `GET /auth/profile` - Obtener perfil de usuario
- `POST /auth/logout` - Cerrar sesión
- `PUT /auth/change-password` - Cambiar contraseña
- `POST /auth/push-token` - Registrar token push
- `GET /auth/push-token` - Obtener token push
- `PUT /auth/push-token` - Actualizar token push
- `DELETE /auth/push-token` - Eliminar token push

### Endpoints de Administración (Requieren rol ADMIN)
- `GET /admin/users` - Listar usuarios
- `POST /admin/users` - Crear usuario
- `GET /admin/users/{id}` - Obtener usuario por ID
- `PUT /admin/users/{id}` - Actualizar usuario
- `DELETE /admin/users/{id}` - Eliminar usuario
- `PATCH /admin/users/{id}/status` - Cambiar estado de usuario
- `GET /admin/users/stats` - Estadísticas de usuarios

## Configuración de Seguridad

Los endpoints de Swagger están configurados como públicos en la configuración de Spring Security:

```java
.requestMatchers("/v3/api-docs/**").permitAll()
.requestMatchers("/swagger-ui/**").permitAll()
.requestMatchers("/swagger-ui.html").permitAll()
.requestMatchers("/swagger-resources/**").permitAll()
.requestMatchers("/webjars/**").permitAll()
```

## Personalización

### Información de la API

La información básica de la API se configura en `SwaggerConfig.java`:

- Título: "GEDSYS Authentication Service API"
- Descripción: Servicio de autenticación y gestión de usuarios
- Versión: 1.0.0
- Contacto: GEDSYS Team
- Servidores: Desarrollo y Producción

### Esquemas de Seguridad

Configurado para usar autenticación Bearer JWT:
- Tipo: HTTP
- Esquema: bearer
- Formato: JWT

## Troubleshooting

### Problemas Comunes

1. **404 en /swagger-ui.html**: Verifica que la aplicación esté ejecutándose y que la dependencia esté incluida
2. **Endpoints no aparecen**: Asegúrate de que los controladores tengan las anotaciones `@RestController`
3. **Autenticación no funciona**: Verifica que el token JWT esté en el formato correcto: `Bearer {token}`

### Logs Útiles

Para debugging, puedes habilitar logs adicionales:

```properties
logging.level.org.springdoc=DEBUG
logging.level.io.swagger=DEBUG
```
