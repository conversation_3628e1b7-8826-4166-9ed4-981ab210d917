package co.com.gedsys.authentication.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.event.ApplicationReadyEvent;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.atLeastOnce;

/**
 * Unit tests for ApplicationStartupValidator.
 */
@ExtendWith(MockitoExtension.class)
class ApplicationStartupValidatorTest {
    
    @Mock
    private JwtProperties jwtProperties;
    
    @Mock
    private DataSource dataSource;
    
    @Mock
    private Connection connection;
    
    @Mock
    private PreparedStatement statement;
    
    @Mock
    private ResultSet resultSet;
    
    @Mock
    private ApplicationReadyEvent applicationReadyEvent;
    
    @InjectMocks
    private ApplicationStartupValidator validator;
    
    @Test
    void shouldValidateValidJwtConfiguration() throws Exception {
        // Given
        when(jwtProperties.getSecret()).thenReturn("a-very-long-secret-key-that-is-at-least-32-characters-long");
        when(jwtProperties.getAccessTokenExpiration()).thenReturn(3600000L);
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(604800000L);
        
        JwtProperties.Mobile mobile = new JwtProperties.Mobile();
        mobile.setAccessTokenExpiration(7200000L);
        when(jwtProperties.getMobile()).thenReturn(mobile);
        
        JwtProperties.Web web = new JwtProperties.Web();
        web.setAccessTokenExpiration(3600000L);
        when(jwtProperties.getWeb()).thenReturn(web);
        
        // Mock database connectivity
        when(dataSource.getConnection()).thenReturn(connection);
        when(connection.isValid(anyInt())).thenReturn(true);
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.executeQuery()).thenReturn(resultSet);
        when(resultSet.next()).thenReturn(true);
        when(resultSet.getInt(1)).thenReturn(1);
        
        // When
        validator.validateApplicationStartup();
        
        // Then - no exceptions should be thrown
        verify(jwtProperties, atLeastOnce()).getSecret();
        verify(dataSource).getConnection();
    }
    
    @Test
    void shouldHandleInvalidJwtConfiguration() throws Exception {
        // Given - invalid JWT configuration
        when(jwtProperties.getSecret()).thenReturn(""); // Empty secret
        when(jwtProperties.getAccessTokenExpiration()).thenReturn(-1L); // Invalid expiration
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(0L); // Invalid expiration
        
        JwtProperties.Mobile mobile = new JwtProperties.Mobile();
        mobile.setAccessTokenExpiration(-1L);
        when(jwtProperties.getMobile()).thenReturn(mobile);
        
        JwtProperties.Web web = new JwtProperties.Web();
        web.setAccessTokenExpiration(0L);
        when(jwtProperties.getWeb()).thenReturn(web);
        
        // Mock database connectivity
        when(dataSource.getConnection()).thenReturn(connection);
        when(connection.isValid(anyInt())).thenReturn(true);
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.executeQuery()).thenReturn(resultSet);
        when(resultSet.next()).thenReturn(true);
        when(resultSet.getInt(1)).thenReturn(1);
        
        // When
        validator.validateApplicationStartup();
        
        // Then - should handle invalid configuration gracefully
        verify(jwtProperties, atLeastOnce()).getSecret();
        verify(dataSource).getConnection();
    }
    
    @Test
    void shouldHandleDatabaseConnectivityFailure() throws Exception {
        // Given - valid JWT configuration
        when(jwtProperties.getSecret()).thenReturn("a-very-long-secret-key-that-is-at-least-32-characters-long");
        when(jwtProperties.getAccessTokenExpiration()).thenReturn(3600000L);
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(604800000L);
        
        JwtProperties.Mobile mobile = new JwtProperties.Mobile();
        when(jwtProperties.getMobile()).thenReturn(mobile);
        
        JwtProperties.Web web = new JwtProperties.Web();
        when(jwtProperties.getWeb()).thenReturn(web);
        
        // Mock database connectivity failure
        when(dataSource.getConnection()).thenThrow(new SQLException("Connection failed"));
        
        // When
        validator.validateApplicationStartup();
        
        // Then - should handle database failure gracefully
        verify(dataSource).getConnection();
    }
    
    @Test
    void shouldHandleInvalidDatabaseConnection() throws Exception {
        // Given - valid JWT configuration
        when(jwtProperties.getSecret()).thenReturn("a-very-long-secret-key-that-is-at-least-32-characters-long");
        when(jwtProperties.getAccessTokenExpiration()).thenReturn(3600000L);
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(604800000L);
        
        JwtProperties.Mobile mobile = new JwtProperties.Mobile();
        when(jwtProperties.getMobile()).thenReturn(mobile);
        
        JwtProperties.Web web = new JwtProperties.Web();
        when(jwtProperties.getWeb()).thenReturn(web);
        
        // Mock invalid database connection
        when(dataSource.getConnection()).thenReturn(connection);
        when(connection.isValid(anyInt())).thenReturn(false);
        
        // When
        validator.validateApplicationStartup();
        
        // Then - should handle invalid connection gracefully
        verify(connection).isValid(anyInt());
    }
    
    @Test
    void shouldHandleMissingDatabaseSchema() throws Exception {
        // Given - valid JWT configuration
        when(jwtProperties.getSecret()).thenReturn("a-very-long-secret-key-that-is-at-least-32-characters-long");
        when(jwtProperties.getAccessTokenExpiration()).thenReturn(3600000L);
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(604800000L);
        
        JwtProperties.Mobile mobile = new JwtProperties.Mobile();
        when(jwtProperties.getMobile()).thenReturn(mobile);
        
        JwtProperties.Web web = new JwtProperties.Web();
        when(jwtProperties.getWeb()).thenReturn(web);
        
        // Mock database with missing schema
        when(dataSource.getConnection()).thenReturn(connection);
        when(connection.isValid(anyInt())).thenReturn(true);
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.executeQuery()).thenReturn(resultSet);
        when(resultSet.next()).thenReturn(true);
        when(resultSet.getInt(1)).thenReturn(0); // No users table found
        
        // When
        validator.validateApplicationStartup();
        
        // Then - should handle missing schema gracefully
        verify(statement).executeQuery();
    }
}