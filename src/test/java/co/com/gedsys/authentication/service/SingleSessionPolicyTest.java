package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for single session policy implementation.
 * Validates that the system correctly enforces one active session per session type.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class SingleSessionPolicyTest {

    @Autowired
    private RefreshTokenService refreshTokenService;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private UserRepository userRepository;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User("<EMAIL>", "password123");
        testUser = userRepository.save(testUser);
    }

    @Nested
    @DisplayName("Single Session Policy Tests")
    class SingleSessionPolicyTests {

        @Test
        @DisplayName("Should allow one active session per session type")
        void shouldAllowOneActiveSessionPerType() {
            // When: Generate tokens for different session types
            RefreshToken webToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            RefreshToken mobileToken = refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE);

            // Then: Both tokens should exist and be active
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(userTokens).hasSize(2);
            assertThat(userTokens).extracting(RefreshToken::getSessionType)
                    .containsExactlyInAnyOrder(SessionType.WEB, SessionType.MOBILE);
            assertThat(userTokens).allMatch(token -> !token.isUsed());
        }

        @Test
        @DisplayName("Should replace existing session when logging in with same session type")
        void shouldReplaceExistingSessionOfSameType() {
            // Given: User has an active WEB session
            RefreshToken firstWebToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            String firstTokenValue = firstWebToken.getToken();

            // When: User logs in again with WEB session type
            RefreshToken secondWebToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);

            // Then: Only the new token should exist
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(userTokens).hasSize(1);
            assertThat(userTokens.get(0).getToken()).isEqualTo(secondWebToken.getToken());
            assertThat(userTokens.get(0).getToken()).isNotEqualTo(firstTokenValue);
            assertThat(userTokens.get(0).getSessionType()).isEqualTo(SessionType.WEB);
        }

        @Test
        @DisplayName("Should not affect other session types when replacing session")
        void shouldNotAffectOtherSessionTypesWhenReplacing() {
            // Given: User has both WEB and MOBILE sessions
            RefreshToken webToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            RefreshToken mobileToken = refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE);

            // When: User logs in again with WEB session type
            RefreshToken newWebToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);

            // Then: MOBILE session should remain, WEB session should be replaced
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(userTokens).hasSize(2);
            
            // Mobile token should remain unchanged
            RefreshToken remainingMobileToken = userTokens.stream()
                    .filter(token -> token.getSessionType() == SessionType.MOBILE)
                    .findFirst().orElseThrow();
            assertThat(remainingMobileToken.getToken()).isEqualTo(mobileToken.getToken());
            
            // Web token should be the new one
            RefreshToken remainingWebToken = userTokens.stream()
                    .filter(token -> token.getSessionType() == SessionType.WEB)
                    .findFirst().orElseThrow();
            assertThat(remainingWebToken.getToken()).isEqualTo(newWebToken.getToken());
            assertThat(remainingWebToken.getToken()).isNotEqualTo(webToken.getToken());
        }
    }

    @Nested
    @DisplayName("Token Consumption Tests")
    class TokenConsumptionTests {

        @Test
        @DisplayName("Should physically delete token after consumption")
        void shouldPhysicallyDeleteTokenAfterConsumption() {
            // Given: User has an active token
            RefreshToken token = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            String tokenValue = token.getToken();

            // When: Token is consumed
            refreshTokenService.validateAndConsumeToken(tokenValue);

            // Then: Token should be physically deleted
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(userTokens).isEmpty();
        }

        @Test
        @DisplayName("Should allow creating new token after previous was consumed")
        void shouldAllowCreatingNewTokenAfterPreviousWasConsumed() {
            // Given: User has a token that gets consumed
            RefreshToken firstToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            refreshTokenService.validateAndConsumeToken(firstToken.getToken());

            // When: User generates a new token of the same type
            RefreshToken secondToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);

            // Then: New token should be created successfully
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(userTokens).hasSize(1);
            assertThat(userTokens.get(0).getToken()).isEqualTo(secondToken.getToken());
            assertThat(userTokens.get(0).getSessionType()).isEqualTo(SessionType.WEB);
        }
    }

    @Nested
    @DisplayName("Logout Tests")
    class LogoutTests {

        @Test
        @DisplayName("Should delete tokens on logout")
        void shouldDeleteTokensOnLogout() {
            // Given: User has active sessions
            refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE);

            // When: User logs out from WEB
            refreshTokenService.invalidateTokensForSessionType(testUser, SessionType.WEB);

            // Then: Only MOBILE session should remain
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(userTokens).hasSize(1);
            assertThat(userTokens.get(0).getSessionType()).isEqualTo(SessionType.MOBILE);
        }
    }
}
