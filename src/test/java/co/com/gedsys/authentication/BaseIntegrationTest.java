package co.com.gedsys.authentication;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;

/**
 * Base class for integration tests that require a real PostgreSQL database.
 * This class uses Spring Docker Compose plugin to automatically start and stop
 * a PostgreSQL container for testing.
 * 
 * The container lifecycle is managed automatically:
 * - Container starts before test class execution
 * - Flyway migrations are applied automatically
 * - Container stops after test class execution
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.docker.compose.file=compose-test.yaml",
    "spring.datasource.url=*********************************************",
    "spring.datasource.username=test_user",
    "spring.datasource.password=test_password",
    "spring.jpa.hibernate.ddl-auto=validate",
    "spring.flyway.enabled=true",
    "spring.flyway.clean-disabled=false",
    "logging.level.org.springframework.docker.compose=DEBUG"
})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public abstract class BaseIntegrationTest {
    
    @Autowired
    protected TestRestTemplate restTemplate;
    
    @Autowired
    protected DataSource dataSource;
    
    /**
     * Setup method called before each test method.
     * Database is automatically started by Spring Docker Compose plugin
     * and Flyway migrations are applied automatically.
     */
    @BeforeEach
    void setUp() {
        // Database is automatically started by Spring Docker Compose plugin
        // Flyway migrations are applied automatically
        // Additional test setup can be added here if needed
    }
    
    /**
     * Teardown method called after each test method.
     * Container will be stopped automatically after test class execution.
     */
    @AfterEach
    void tearDown() {
        // Clean up test data if needed
        // Container will be stopped automatically after test class
        // Data cleanup is handled by @DirtiesContext
    }
    
    /**
     * Utility method to verify database connectivity.
     * Can be used in tests to ensure the database is properly configured.
     * 
     * @return true if database connection is successful
     */
    protected boolean isDatabaseConnected() {
        try {
            return dataSource.getConnection().isValid(5);
        } catch (Exception e) {
            return false;
        }
    }
}