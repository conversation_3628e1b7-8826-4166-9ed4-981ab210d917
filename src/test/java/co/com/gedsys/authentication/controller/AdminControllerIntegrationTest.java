package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.BaseIntegrationTest;
import co.com.gedsys.authentication.dto.AdminUserResponse;
import co.com.gedsys.authentication.dto.CreateUserRequest;
import co.com.gedsys.authentication.dto.PagedResponse;
import co.com.gedsys.authentication.dto.UpdateUserRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.annotation.DirtiesContext;

import java.time.LocalDateTime;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for AdminController.
 * Tests pagination, filtering, role-based access control, and CRUD operations.
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class AdminControllerIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private PushTokenRepository pushTokenRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ObjectMapper objectMapper;

    private User adminUser;
    private User regularUser;
    private final String adminPassword = "admin123";
    private final String adminEmail = "<EMAIL>";
    private final String userPassword = "user123";
    private final String userEmail = "<EMAIL>";

    @BeforeEach
    void setUpTestData() {
        // Clean up any existing data
        pushTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();
        
        // Create admin user
        adminUser = new User();
        adminUser.setEmail(adminEmail);
        adminUser.setPassword(passwordEncoder.encode(adminPassword));
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setRole(Role.ADMIN);
        adminUser.setEnabled(true);
        adminUser.setCreatedAt(LocalDateTime.now());
        adminUser.setUpdatedAt(LocalDateTime.now());
        adminUser = userRepository.save(adminUser);

        // Create regular user
        regularUser = new User();
        regularUser.setEmail(userEmail);
        regularUser.setPassword(passwordEncoder.encode(userPassword));
        regularUser.setFirstName("Regular");
        regularUser.setLastName("User");
        regularUser.setRole(Role.USER);
        regularUser.setEnabled(true);
        regularUser.setCreatedAt(LocalDateTime.now());
        regularUser.setUpdatedAt(LocalDateTime.now());
        regularUser = userRepository.save(regularUser);
    }
    
    @org.junit.jupiter.api.AfterEach
    void cleanUpTestData() {
        // Clean up test data
        pushTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();
    }

    @Test
    void testGetUsersWithAdminRole_ShouldReturnPaginatedUsers() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();

        // When - Get users
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<PagedResponse> response = restTemplate.exchange(
                "/admin/users", HttpMethod.GET, requestEntity, PagedResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        PagedResponse pagedResponse = response.getBody();
        assertThat(pagedResponse.getContent()).hasSize(2); // admin + regular user
        assertThat(pagedResponse.getTotalElements()).isEqualTo(2);
        assertThat(pagedResponse.getPage()).isEqualTo(0);
        assertThat(pagedResponse.getSize()).isEqualTo(20);
    }

    @Test
    void testGetUsersWithRegularUserRole_ShouldReturnForbidden() {
        // Given - Login as regular user
        String userToken = loginAsRegularUser();

        // When - Try to get users
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(userToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(
                "/admin/users", HttpMethod.GET, requestEntity, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.FORBIDDEN);
    }

    @Test
    void testGetUsersWithoutAuthentication_ShouldReturnUnauthorized() {
        // When - Try to get users without authentication
        ResponseEntity<String> response = restTemplate.getForEntity("/admin/users", String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void testGetUsersWithPaginationAndFilters_ShouldReturnFilteredResults() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();

        // When - Get users with email filter
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<PagedResponse> response = restTemplate.exchange(
                "/admin/users?email=admin&page=0&size=10", HttpMethod.GET, requestEntity, PagedResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        PagedResponse pagedResponse = response.getBody();
        assertThat(pagedResponse.getContent()).hasSize(1); // Only admin user matches
        assertThat(pagedResponse.getTotalElements()).isEqualTo(1);
    }

    @Test
    void testGetUserById_ShouldReturnDetailedUserInfo() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();

        // When - Get user by ID
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<AdminUserResponse> response = restTemplate.exchange(
                "/admin/users/" + regularUser.getId(), HttpMethod.GET, requestEntity, AdminUserResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        AdminUserResponse userResponse = response.getBody();
        assertThat(userResponse.getId()).isEqualTo(regularUser.getId());
        assertThat(userResponse.getEmail()).isEqualTo(userEmail);
        assertThat(userResponse.getFirstName()).isEqualTo("Regular");
        assertThat(userResponse.getLastName()).isEqualTo("User");
        assertThat(userResponse.getRole()).isEqualTo(Role.USER);
        assertThat(userResponse.isEnabled()).isTrue();
        assertThat(userResponse.getActiveSessions()).isNotNull();
    }

    @Test
    void testCreateUser_ShouldCreateNewUser() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();
        
        CreateUserRequest createRequest = new CreateUserRequest(
                "<EMAIL>", "password123", "New", "User", Role.USER, true);

        // When - Create user
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<CreateUserRequest> requestEntity = new HttpEntity<>(createRequest, headers);

        ResponseEntity<UserProfileResponse> response = restTemplate.exchange(
                "/admin/users", HttpMethod.POST, requestEntity, UserProfileResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        
        UserProfileResponse userResponse = response.getBody();
        assertThat(userResponse.getEmail()).isEqualTo("<EMAIL>");
        assertThat(userResponse.getFirstName()).isEqualTo("New");
        assertThat(userResponse.getLastName()).isEqualTo("User");
        assertThat(userResponse.getRole()).isEqualTo(Role.USER);

        // Verify user was created in database
        assertThat(userRepository.findByEmail("<EMAIL>")).isPresent();
    }

    @Test
    void testUpdateUser_ShouldUpdateExistingUser() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();
        
        UpdateUserRequest updateRequest = new UpdateUserRequest(
                "<EMAIL>", null, "Updated", "Name", Role.USER, true);

        // When - Update user
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateUserRequest> requestEntity = new HttpEntity<>(updateRequest, headers);

        ResponseEntity<UserProfileResponse> response = restTemplate.exchange(
                "/admin/users/" + regularUser.getId(), HttpMethod.PUT, requestEntity, UserProfileResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        UserProfileResponse userResponse = response.getBody();
        assertThat(userResponse.getEmail()).isEqualTo("<EMAIL>");
        assertThat(userResponse.getFirstName()).isEqualTo("Updated");
        assertThat(userResponse.getLastName()).isEqualTo("Name");

        // Verify user was updated in database
        User updatedUser = userRepository.findById(regularUser.getId()).orElse(null);
        assertThat(updatedUser).isNotNull();
        assertThat(updatedUser.getEmail()).isEqualTo("<EMAIL>");
        assertThat(updatedUser.getFirstName()).isEqualTo("Updated");
    }

    @Test
    void testDeleteUser_ShouldDeleteUser() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();

        // When - Delete user
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/admin/users/" + regularUser.getId(), HttpMethod.DELETE, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("message")).isEqualTo("User deleted successfully");

        // Verify user was deleted from database
        assertThat(userRepository.findById(regularUser.getId())).isEmpty();
    }

    @Test
    void testSetUserEnabled_ShouldUpdateUserStatus() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();
        
        Map<String, Boolean> enabledRequest = Map.of("enabled", false);

        // When - Disable user
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Boolean>> requestEntity = new HttpEntity<>(enabledRequest, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/admin/users/" + regularUser.getId() + "/enabled", HttpMethod.PATCH, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("message")).isEqualTo("User disabled successfully");
        assertThat(response.getBody().get("enabled")).isEqualTo(false);

        // Verify user was disabled in database
        User disabledUser = userRepository.findById(regularUser.getId()).orElse(null);
        assertThat(disabledUser).isNotNull();
        assertThat(disabledUser.isEnabled()).isFalse();
    }

    @Test
    void testInvalidateUserSessions_ShouldInvalidateSessions() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();

        // When - Invalidate user sessions
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/admin/users/" + regularUser.getId() + "/invalidate-sessions", HttpMethod.POST, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("message")).isEqualTo("All user sessions invalidated successfully");
    }

    @Test
    void testGetUserStats_ShouldReturnStatistics() {
        // Given - Login as admin
        String adminToken = loginAsAdmin();

        // When - Get user statistics
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(adminToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/admin/users/stats", HttpMethod.GET, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> stats = response.getBody();
        assertThat(stats.get("totalUsers")).isEqualTo(2);
        assertThat(stats.get("enabledUsers")).isEqualTo(2);
        assertThat(stats.get("disabledUsers")).isEqualTo(0);
        assertThat(stats.get("adminUsers")).isEqualTo(1);
        assertThat(stats.get("regularUsers")).isEqualTo(1);
        assertThat(stats.get("totalPushTokens")).isEqualTo(0);
    }

    private String loginAsAdmin() {
        return loginUser(adminEmail, adminPassword);
    }

    private String loginAsRegularUser() {
        return loginUser(userEmail, userPassword);
    }

    private String loginUser(String email, String password) {
        Map<String, Object> loginRequest = Map.of(
                "email", email,
                "password", password,
                "sessionType", "WEB"
        );

        ResponseEntity<Map> response = restTemplate.postForEntity("/auth/login", loginRequest, Map.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        return (String) response.getBody().get("accessToken");
    }
}