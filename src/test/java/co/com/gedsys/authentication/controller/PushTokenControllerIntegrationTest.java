package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.BaseIntegrationTest;
import co.com.gedsys.authentication.dto.PushTokenRequest;
import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.annotation.DirtiesContext;

import java.time.LocalDateTime;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for PushTokenController.
 * Tests single token per user policy, cleanup on logout, and mobile session requirements.
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class PushTokenControllerIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private PushTokenRepository pushTokenRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private final String testPassword = "password123";
    private final String testEmail = "<EMAIL>";

    @BeforeEach
    void setUpTestData() {
        // Clean up any existing data
        pushTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();
        
        // Create test user
        testUser = new User();
        testUser.setEmail(testEmail);
        testUser.setPassword(passwordEncoder.encode(testPassword));
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());
        
        testUser = userRepository.save(testUser);
    }
    
    @org.junit.jupiter.api.AfterEach
    void cleanUpTestData() {
        // Clean up test data
        pushTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();
    }

    @Test
    void testRegisterPushTokenWithMobileSession_ShouldSucceed() {
        // Given - Login with mobile session
        String mobileToken = loginWithMobileSession();
        
        PushTokenRequest pushTokenRequest = new PushTokenRequest(
                "test-push-token-123", DeviceType.ANDROID, "device-123");

        // When - Register push token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> requestEntity = new HttpEntity<>(pushTokenRequest, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.POST, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("message")).isEqualTo("Push token registered successfully");
        assertThat(response.getBody().get("pushToken")).isNotNull();

        // Verify push token is stored in database
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();
    }

    @Test
    void testRegisterPushTokenWithWebSession_ShouldReturnForbidden() {
        // Given - Login with web session
        String webToken = loginWithWebSession();
        
        PushTokenRequest pushTokenRequest = new PushTokenRequest(
                "test-push-token-123", DeviceType.ANDROID, "device-123");

        // When - Try to register push token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(webToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> requestEntity = new HttpEntity<>(pushTokenRequest, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.POST, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.FORBIDDEN);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("error"))
                .isEqualTo("Push tokens can only be managed from mobile sessions");
    }

    @Test
    void testRegisterPushTokenWithoutAuthentication_ShouldReturnUnauthorized() {
        // Given
        PushTokenRequest pushTokenRequest = new PushTokenRequest(
                "test-push-token-123", DeviceType.ANDROID, "device-123");

        // When - Try to register push token without authentication
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> requestEntity = new HttpEntity<>(pushTokenRequest, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.POST, requestEntity, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void testSingleTokenPerUserPolicy_ShouldReplaceExistingToken() {
        // Given - Login with mobile session and register first token
        String mobileToken = loginWithMobileSession();
        
        PushTokenRequest firstTokenRequest = new PushTokenRequest(
                "first-push-token", DeviceType.ANDROID, "device-1");

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> firstRequestEntity = new HttpEntity<>(firstTokenRequest, headers);

        ResponseEntity<Map> firstResponse = restTemplate.exchange(
                "/auth/push-token", HttpMethod.POST, firstRequestEntity, Map.class);
        assertThat(firstResponse.getStatusCode()).isEqualTo(HttpStatus.OK);

        // When - Register second token (should replace first)
        PushTokenRequest secondTokenRequest = new PushTokenRequest(
                "second-push-token", DeviceType.IOS, "device-2");
        HttpEntity<PushTokenRequest> secondRequestEntity = new HttpEntity<>(secondTokenRequest, headers);

        ResponseEntity<Map> secondResponse = restTemplate.exchange(
                "/auth/push-token", HttpMethod.POST, secondRequestEntity, Map.class);

        // Then
        assertThat(secondResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(secondResponse.getBody().get("message")).isEqualTo("Push token registered successfully");

        // Verify only one push token exists for user
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();
        assertThat(pushTokenRepository.findByUser(testUser).get().getToken()).isEqualTo("second-push-token");
        assertThat(pushTokenRepository.findByUser(testUser).get().getDeviceType()).isEqualTo(DeviceType.IOS);
    }

    @Test
    void testGetPushToken_ShouldReturnTokenInfo() {
        // Given - Login with mobile session and register token
        String mobileToken = loginWithMobileSession();
        registerPushToken(mobileToken, "test-push-token", DeviceType.ANDROID, "device-123");

        // When - Get push token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.GET, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("message")).isEqualTo("Push token retrieved successfully");
        assertThat(response.getBody().get("pushToken")).isNotNull();
    }

    @Test
    void testGetPushTokenWithoutToken_ShouldReturnNoToken() {
        // Given - Login with mobile session but no token registered
        String mobileToken = loginWithMobileSession();

        // When - Get push token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.GET, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().get("message")).isEqualTo("No push token registered");
        assertThat(response.getBody().get("pushToken")).isNull();
    }

    @Test
    void testUpdatePushToken_ShouldUpdateExistingToken() {
        // Given - Login with mobile session and register token
        String mobileToken = loginWithMobileSession();
        registerPushToken(mobileToken, "original-token", DeviceType.ANDROID, "device-1");

        // When - Update push token
        PushTokenRequest updateRequest = new PushTokenRequest(
                "updated-token", DeviceType.IOS, "device-2");

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> requestEntity = new HttpEntity<>(updateRequest, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.PUT, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().get("message")).isEqualTo("Push token updated successfully");

        // Verify token was updated
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();
        assertThat(pushTokenRepository.findByUser(testUser).get().getToken()).isEqualTo("updated-token");
        assertThat(pushTokenRepository.findByUser(testUser).get().getDeviceType()).isEqualTo(DeviceType.IOS);
    }

    @Test
    void testUpdatePushTokenWithoutExisting_ShouldReturnBadRequest() {
        // Given - Login with mobile session but no token registered
        String mobileToken = loginWithMobileSession();

        // When - Try to update non-existent push token
        PushTokenRequest updateRequest = new PushTokenRequest(
                "updated-token", DeviceType.IOS, "device-2");

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> requestEntity = new HttpEntity<>(updateRequest, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.PUT, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody().get("error"))
                .isEqualTo("No existing push token to update. Use POST to register a new token.");
    }

    @Test
    void testDeletePushToken_ShouldRemoveToken() {
        // Given - Login with mobile session and register token
        String mobileToken = loginWithMobileSession();
        registerPushToken(mobileToken, "test-token", DeviceType.ANDROID, "device-123");

        // Verify token exists
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();

        // When - Delete push token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.DELETE, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().get("message")).isEqualTo("Push token deleted successfully");

        // Verify token was removed
        assertThat(pushTokenRepository.findByUser(testUser)).isEmpty();
    }

    @Test
    void testDeletePushTokenWithoutExisting_ShouldReturnOk() {
        // Given - Login with mobile session but no token registered
        String mobileToken = loginWithMobileSession();

        // When - Try to delete non-existent push token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.DELETE, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().get("message")).isEqualTo("No push token found to delete");
    }

    @Test
    void testHasPushToken_ShouldReturnCorrectStatus() {
        // Given - Login with mobile session
        String mobileToken = loginWithMobileSession();

        // When - Check if user has push token (should be false initially)
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token/exists", HttpMethod.GET, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().get("hasPushToken")).isEqualTo(false);
        assertThat(response.getBody().get("message")).isEqualTo("User does not have push token");

        // Given - Register push token
        registerPushToken(mobileToken, "test-token", DeviceType.ANDROID, "device-123");

        // When - Check again (should be true now)
        ResponseEntity<Map> response2 = restTemplate.exchange(
                "/auth/push-token/exists", HttpMethod.GET, requestEntity, Map.class);

        // Then
        assertThat(response2.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response2.getBody().get("hasPushToken")).isEqualTo(true);
        assertThat(response2.getBody().get("message")).isEqualTo("User has push token");
    }

    @Test
    void testCleanupOnLogout_ShouldRemovePushToken() {
        // Given - Login with mobile session and register push token
        String mobileToken = loginWithMobileSession();
        registerPushToken(mobileToken, "test-token", DeviceType.ANDROID, "device-123");

        // Verify token exists
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();

        // When - Logout from mobile session
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(mobileToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, String> logoutRequest = Map.of("sessionType", "MOBILE");
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(logoutRequest, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/logout", HttpMethod.POST, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);

        // Verify push token was removed on logout
        assertThat(pushTokenRepository.findByUser(testUser)).isEmpty();
    }

    private String loginWithMobileSession() {
        return loginUser(SessionType.MOBILE);
    }

    private String loginWithWebSession() {
        return loginUser(SessionType.WEB);
    }

    private String loginUser(SessionType sessionType) {
        Map<String, Object> loginRequest = Map.of(
                "email", testEmail,
                "password", testPassword,
                "sessionType", sessionType.name()
        );

        ResponseEntity<Map> response = restTemplate.postForEntity("/auth/login", loginRequest, Map.class);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        return (String) response.getBody().get("accessToken");
    }

    private void registerPushToken(String accessToken, String pushToken, DeviceType deviceType, String deviceId) {
        PushTokenRequest pushTokenRequest = new PushTokenRequest(pushToken, deviceType, deviceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushTokenRequest> requestEntity = new HttpEntity<>(pushTokenRequest, headers);

        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/push-token", HttpMethod.POST, requestEntity, Map.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}