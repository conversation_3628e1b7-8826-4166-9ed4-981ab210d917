package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.BaseIntegrationTest;
import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.ChangePasswordRequest;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.dto.RefreshTokenRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.annotation.DirtiesContext;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for AuthenticationController.
 * Tests complete login/logout/refresh flows, session management, token cleanup,
 * and password change functionality.
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class AuthenticationControllerIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private PushTokenRepository pushTokenRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private final String testPassword = "password123";
    private final String testEmail = "<EMAIL>";

    @BeforeEach
    void setUpTestData() {
        // Clean up any existing data
        pushTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();
        
        // Create test user
        testUser = new User();
        testUser.setEmail(testEmail);
        testUser.setPassword(passwordEncoder.encode(testPassword));
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());
        
        testUser = userRepository.save(testUser);
    }
    
    @org.junit.jupiter.api.AfterEach
    void cleanUpTestData() {
        // Clean up test data
        pushTokenRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        userRepository.deleteAll();
    }

    @Test
    void testLoginWithWebSession_ShouldReturnTokensAndUserProfile() {
        // Given
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);

        // When
        ResponseEntity<AuthResponse> response = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        AuthResponse authResponse = response.getBody();
        assertThat(authResponse.getAccessToken()).isNotNull();
        assertThat(authResponse.getRefreshToken()).isNotNull();
        assertThat(authResponse.getTokenType()).isEqualTo("Bearer");
        assertThat(authResponse.getExpiresIn()).isPositive();
        
        UserProfileResponse userProfile = authResponse.getUser();
        assertThat(userProfile).isNotNull();
        assertThat(userProfile.getEmail()).isEqualTo(testEmail);
        assertThat(userProfile.getFirstName()).isEqualTo("Test");
        assertThat(userProfile.getLastName()).isEqualTo("User");
        assertThat(userProfile.getRole()).isEqualTo(Role.USER);

        // Verify refresh token is stored in database
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                .isPresent();
    }

    @Test
    void testLoginWithMobileSession_ShouldReturnTokensAndStorePushToken() {
        // Given
        String pushToken = "test-push-token-123";
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.MOBILE,
                pushToken, DeviceType.ANDROID, "device-123");

        // When
        ResponseEntity<AuthResponse> response = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        AuthResponse authResponse = response.getBody();
        assertThat(authResponse.getAccessToken()).isNotNull();
        assertThat(authResponse.getRefreshToken()).isNotNull();

        // Verify mobile refresh token is stored
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE))
                .isPresent();

        // Verify push token is stored
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();
    }

    @Test
    void testLoginWithInvalidCredentials_ShouldReturnUnauthorized() {
        // Given
        LoginRequest loginRequest = new LoginRequest(testEmail, "wrongpassword", SessionType.WEB);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                "/auth/login", loginRequest, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void testRefreshToken_ShouldReturnNewTokens() {
        // Given - Login first to get refresh token
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String refreshToken = loginResponse.getBody().getRefreshToken();
        RefreshTokenRequest refreshRequest = new RefreshTokenRequest(refreshToken);

        // When
        ResponseEntity<AuthResponse> response = restTemplate.postForEntity(
                "/auth/refresh", refreshRequest, AuthResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        AuthResponse authResponse = response.getBody();
        assertThat(authResponse.getAccessToken()).isNotNull();
        assertThat(authResponse.getRefreshToken()).isNotNull();
        
        // New refresh token should be different from the old one
        assertThat(authResponse.getRefreshToken()).isNotEqualTo(refreshToken);
    }

    @Test
    void testRefreshTokenSingleUse_ShouldFailOnSecondUse() {
        // Given - Login and get refresh token
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String refreshToken = loginResponse.getBody().getRefreshToken();
        RefreshTokenRequest refreshRequest = new RefreshTokenRequest(refreshToken);

        // When - Use refresh token first time
        ResponseEntity<AuthResponse> firstRefresh = restTemplate.postForEntity(
                "/auth/refresh", refreshRequest, AuthResponse.class);
        
        // Then - First use should succeed
        assertThat(firstRefresh.getStatusCode()).isEqualTo(HttpStatus.OK);

        // When - Try to use the same refresh token again
        ResponseEntity<String> secondRefresh = restTemplate.postForEntity(
                "/auth/refresh", refreshRequest, String.class);

        // Then - Second use should fail
        assertThat(secondRefresh.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void testLogoutWebSession_ShouldInvalidateRefreshToken() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // When - Logout
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, String> logoutRequest = Map.of("sessionType", "WEB");
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(logoutRequest, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/logout", HttpMethod.POST, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().get("message")).isEqualTo("Logout successful");

        // Verify refresh token is invalidated (marked as used)
        Optional<RefreshToken> tokenOpt = 
                refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB);
        assertThat(tokenOpt).isPresent();
        assertThat(tokenOpt.get().isUsed()).isTrue();
    }

    @Test
    void testLogoutMobileSession_ShouldInvalidateTokensAndRemovePushToken() {
        // Given - Login with mobile session and push token
        String pushToken = "test-push-token-123";
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.MOBILE,
                pushToken, DeviceType.ANDROID, "device-123");
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // Verify push token exists
        assertThat(pushTokenRepository.findByUser(testUser)).isPresent();
        
        // When - Logout from mobile
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, String> logoutRequest = Map.of("sessionType", "MOBILE");
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(logoutRequest, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/logout", HttpMethod.POST, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);

        // Verify mobile refresh token is invalidated (marked as used)
        Optional<RefreshToken> mobileTokenOpt = 
                refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE);
        assertThat(mobileTokenOpt).isPresent();
        assertThat(mobileTokenOpt.get().isUsed()).isTrue();

        // Verify push token is removed
        assertThat(pushTokenRepository.findByUser(testUser)).isEmpty();
    }

    @Test
    void testGetProfile_ShouldReturnUserProfile() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // When - Get profile
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        
        ResponseEntity<UserProfileResponse> response = restTemplate.exchange(
                "/auth/profile", HttpMethod.GET, requestEntity, UserProfileResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        UserProfileResponse profile = response.getBody();
        assertThat(profile.getEmail()).isEqualTo(testEmail);
        assertThat(profile.getFirstName()).isEqualTo("Test");
        assertThat(profile.getLastName()).isEqualTo("User");
        assertThat(profile.getRole()).isEqualTo(Role.USER);
    }

    @Test
    void testGetProfileWithoutToken_ShouldReturnUnauthorized() {
        // When - Try to get profile without token
        ResponseEntity<String> response = restTemplate.getForEntity("/auth/profile", String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void testChangePassword_ShouldUpdatePasswordAndInvalidateAllSessions() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // Also create a mobile session
        LoginRequest mobileLoginRequest = new LoginRequest(testEmail, testPassword, SessionType.MOBILE);
        restTemplate.postForEntity("/auth/login", mobileLoginRequest, AuthResponse.class);
        
        // Verify both sessions exist
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                .isPresent();
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE))
                .isPresent();
        
        // When - Change password
        String newPassword = "newpassword123";
        ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest(
                testPassword, newPassword, newPassword);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChangePasswordRequest> requestEntity = new HttpEntity<>(changePasswordRequest, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/change-password", HttpMethod.PUT, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().get("message"))
                .isEqualTo("Password changed successfully. Please login again.");

        // Verify all sessions are invalidated (marked as used)
        Optional<RefreshToken> webTokenOpt = 
                refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB);
        Optional<RefreshToken> mobileTokenOpt = 
                refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE);
        
        assertThat(webTokenOpt).isPresent();
        assertThat(webTokenOpt.get().isUsed()).isTrue();
        assertThat(mobileTokenOpt).isPresent();
        assertThat(mobileTokenOpt.get().isUsed()).isTrue();

        // Verify new password works
        LoginRequest newLoginRequest = new LoginRequest(testEmail, newPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> newLoginResponse = restTemplate.postForEntity(
                "/auth/login", newLoginRequest, AuthResponse.class);
        assertThat(newLoginResponse.getStatusCode()).isEqualTo(HttpStatus.OK);

        // Verify old password doesn't work
        LoginRequest oldLoginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<String> oldLoginResponse = restTemplate.postForEntity(
                "/auth/login", oldLoginRequest, String.class);
        assertThat(oldLoginResponse.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }

    @Test
    void testChangePasswordWithWrongCurrentPassword_ShouldReturnBadRequest() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // When - Try to change password with wrong current password
        ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest(
                "wrongpassword", "newpassword123", "newpassword123");
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChangePasswordRequest> requestEntity = new HttpEntity<>(changePasswordRequest, headers);
        
        ResponseEntity<String> response = restTemplate.exchange(
                "/auth/change-password", HttpMethod.PUT, requestEntity, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
    }

    @Test
    void testChangePasswordWithMismatchedConfirmation_ShouldReturnBadRequest() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // When - Try to change password with mismatched confirmation
        ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest(
                testPassword, "newpassword123", "differentpassword");
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChangePasswordRequest> requestEntity = new HttpEntity<>(changePasswordRequest, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/change-password", HttpMethod.PUT, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody().get("error"))
                .isEqualTo("New password and confirmation do not match");
    }

    @Test
    void testChangePasswordWithSamePassword_ShouldReturnBadRequest() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // When - Try to change password to the same password
        ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest(
                testPassword, testPassword, testPassword);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChangePasswordRequest> requestEntity = new HttpEntity<>(changePasswordRequest, headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/change-password", HttpMethod.PUT, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody().get("error"))
                .isEqualTo("New password must be different from current password");
    }

    @Test
    void testValidateToken_ShouldReturnUserProfile() {
        // Given - Login first
        LoginRequest loginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> loginResponse = restTemplate.postForEntity(
                "/auth/login", loginRequest, AuthResponse.class);
        
        String accessToken = loginResponse.getBody().getAccessToken();
        
        // When - Validate token
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        
        ResponseEntity<UserProfileResponse> response = restTemplate.exchange(
                "/auth/validate", HttpMethod.GET, requestEntity, UserProfileResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getEmail()).isEqualTo(testEmail);
    }

    @Test
    void testGetActiveSessions_ShouldReturnSessionInfo() {
        // Given - Login with both web and mobile sessions
        LoginRequest webLoginRequest = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> webLoginResponse = restTemplate.postForEntity(
                "/auth/login", webLoginRequest, AuthResponse.class);
        
        LoginRequest mobileLoginRequest = new LoginRequest(testEmail, testPassword, SessionType.MOBILE);
        restTemplate.postForEntity("/auth/login", mobileLoginRequest, AuthResponse.class);
        
        String accessToken = webLoginResponse.getBody().getAccessToken();
        
        // When - Get active sessions
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        
        ResponseEntity<Map> response = restTemplate.exchange(
                "/auth/sessions", HttpMethod.GET, requestEntity, Map.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        Map<String, Object> sessionInfo = response.getBody();
        assertThat(sessionInfo.get("hasMobileSession")).isEqualTo(true);
        assertThat(sessionInfo.get("hasWebSession")).isEqualTo(true);
        assertThat(sessionInfo.get("totalActiveSessions")).isEqualTo(2);
    }

    @Test
    void testSessionManagement_SingleSessionPerType() {
        // Given - Login with web session first
        LoginRequest firstWebLogin = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> firstResponse = restTemplate.postForEntity(
                "/auth/login", firstWebLogin, AuthResponse.class);
        
        String firstRefreshToken = firstResponse.getBody().getRefreshToken();
        
        // When - Login with web session again (should invalidate first session)
        LoginRequest secondWebLogin = new LoginRequest(testEmail, testPassword, SessionType.WEB);
        ResponseEntity<AuthResponse> secondResponse = restTemplate.postForEntity(
                "/auth/login", secondWebLogin, AuthResponse.class);
        
        String secondRefreshToken = secondResponse.getBody().getRefreshToken();
        
        // Then - First refresh token should be invalid
        RefreshTokenRequest firstRefreshRequest = new RefreshTokenRequest(firstRefreshToken);
        ResponseEntity<String> firstRefreshResponse = restTemplate.postForEntity(
                "/auth/refresh", firstRefreshRequest, String.class);
        assertThat(firstRefreshResponse.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        
        // Second refresh token should work
        RefreshTokenRequest secondRefreshRequest = new RefreshTokenRequest(secondRefreshToken);
        ResponseEntity<AuthResponse> secondRefreshResponse = restTemplate.postForEntity(
                "/auth/refresh", secondRefreshRequest, AuthResponse.class);
        assertThat(secondRefreshResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}