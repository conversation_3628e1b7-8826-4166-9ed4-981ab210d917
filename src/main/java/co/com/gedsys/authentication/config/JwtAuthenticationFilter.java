package co.com.gedsys.authentication.config;

import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * JWT Authentication Filter that processes JWT tokens from the Authorization header.
 * Extends OncePerRequestFilter to ensure it's executed once per request.
 * Sets the security context when a valid JWT token is provided.
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    
    private final JwtService jwtService;
    private final UserRepository userRepository;
    
    public JwtAuthenticationFilter(JwtService jwtService, UserRepository userRepository) {
        this.jwtService = jwtService;
        this.userRepository = userRepository;
    }
    
    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws ServletException, IOException {
        
        try {
            String jwt = extractJwtFromRequest(request);
            
            if (jwt != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                authenticateUser(jwt, request);
            }
        } catch (Exception e) {
            logger.debug("JWT authentication failed: {}", e.getMessage());
            // Clear security context on authentication failure
            SecurityContextHolder.clearContext();
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * Extract JWT token from Authorization header
     */
    private String extractJwtFromRequest(HttpServletRequest request) {
        String authorizationHeader = request.getHeader(AUTHORIZATION_HEADER);
        
        if (authorizationHeader != null && authorizationHeader.startsWith(BEARER_PREFIX)) {
            return authorizationHeader.substring(BEARER_PREFIX.length());
        }
        
        return null;
    }
    
    /**
     * Authenticate user based on JWT token
     */
    private void authenticateUser(String jwt, HttpServletRequest request) {
        try {
            // First validate token structure and expiration
            if (!jwtService.isTokenValid(jwt)) {
                logger.debug("Invalid or expired JWT token");
                return;
            }
            
            // Extract user information from token
            String userEmail = jwtService.extractUsername(jwt);
            Long userId = jwtService.extractUserId(jwt);
            
            if (userEmail == null || userId == null) {
                logger.debug("JWT token missing required user information");
                return;
            }
            
            // Load user from database
            Optional<User> userOptional = userRepository.findByEmail(userEmail);
            if (userOptional.isEmpty()) {
                logger.debug("User not found for email: {}", userEmail);
                return;
            }
            
            User user = userOptional.get();
            
            // Validate token against user
            if (!jwtService.validateToken(jwt, user)) {
                logger.debug("JWT token validation failed for user: {}", userEmail);
                return;
            }
            
            // Create authentication token
            UsernamePasswordAuthenticationToken authToken = createAuthenticationToken(user, request);
            
            // Set authentication in security context
            SecurityContextHolder.getContext().setAuthentication(authToken);
            
            logger.debug("Successfully authenticated user: {} with role: {}", userEmail, user.getRole());
            
        } catch (Exception e) {
            logger.debug("Error during JWT authentication: {}", e.getMessage());
            throw e;
        }
    }
    
    /**
     * Create authentication token with user details and authorities
     */
    private UsernamePasswordAuthenticationToken createAuthenticationToken(User user, HttpServletRequest request) {
        // Create authorities based on user role
        List<SimpleGrantedAuthority> authorities = List.of(
            new SimpleGrantedAuthority("ROLE_" + user.getRole().name())
        );
        
        // Create authentication token
        UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
            user.getEmail(),
            null, // No credentials needed for JWT authentication
            authorities
        );
        
        // Set additional details
        authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        
        return authToken;
    }
    
    /**
     * Skip filter for public endpoints that don't require authentication
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();

        // Skip authentication for public endpoints
        return path.startsWith("/auth/login") ||
               path.startsWith("/auth/refresh") ||
               path.startsWith("/actuator/health") ||
               path.startsWith("/v3/api-docs") ||
               path.startsWith("/swagger-ui") ||
               path.startsWith("/swagger-resources") ||
               path.startsWith("/webjars") ||
               path.startsWith("/error") ||
               path.startsWith("/favicon.ico");
    }
}