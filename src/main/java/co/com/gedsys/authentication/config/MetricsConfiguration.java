package co.com.gedsys.authentication.config;

import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Configuration for custom application metrics.
 * Provides business-specific metrics for monitoring authentication service health.
 */
@Configuration
@EnableScheduling
public class MetricsConfiguration {
    
    private final MeterRegistry meterRegistry;
    private final UserRepository userRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PushTokenRepository pushTokenRepository;
    
    // Atomic counters for metrics
    private final AtomicLong totalUsers = new AtomicLong(0);
    private final AtomicLong enabledUsers = new AtomicLong(0);
    private final AtomicLong disabledUsers = new AtomicLong(0);
    private final AtomicLong adminUsers = new AtomicLong(0);
    private final AtomicLong activeRefreshTokens = new AtomicLong(0);
    private final AtomicLong expiredRefreshTokens = new AtomicLong(0);
    private final AtomicLong activePushTokens = new AtomicLong(0);
    private final AtomicLong stalePushTokens = new AtomicLong(0);
    
    public MetricsConfiguration(MeterRegistry meterRegistry,
                              UserRepository userRepository,
                              RefreshTokenRepository refreshTokenRepository,
                              PushTokenRepository pushTokenRepository) {
        this.meterRegistry = meterRegistry;
        this.userRepository = userRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.pushTokenRepository = pushTokenRepository;
        
        // Register gauges for user metrics
        Gauge.builder("auth.users.total", totalUsers, AtomicLong::doubleValue)
                .description("Total number of users in the system")
                .register(meterRegistry);
        
        Gauge.builder("auth.users.enabled", enabledUsers, AtomicLong::doubleValue)
                .description("Number of enabled users")
                .register(meterRegistry);
        
        Gauge.builder("auth.users.disabled", disabledUsers, AtomicLong::doubleValue)
                .description("Number of disabled users")
                .register(meterRegistry);
        
        Gauge.builder("auth.users.admin", adminUsers, AtomicLong::doubleValue)
                .description("Number of admin users")
                .register(meterRegistry);
        
        // Register gauges for token metrics
        Gauge.builder("auth.tokens.refresh.active", activeRefreshTokens, AtomicLong::doubleValue)
                .description("Number of active refresh tokens")
                .register(meterRegistry);
        
        Gauge.builder("auth.tokens.refresh.expired", expiredRefreshTokens, AtomicLong::doubleValue)
                .description("Number of expired refresh tokens")
                .register(meterRegistry);
        
        Gauge.builder("auth.tokens.push.active", activePushTokens, AtomicLong::doubleValue)
                .description("Number of active push tokens")
                .register(meterRegistry);
        
        Gauge.builder("auth.tokens.push.stale", stalePushTokens, AtomicLong::doubleValue)
                .description("Number of stale push tokens (not used in 30 days)")
                .register(meterRegistry);
        
        // Initialize metrics
        updateMetrics();
    }
    
    /**
     * Update all metrics periodically.
     * Runs every 5 minutes to keep metrics current.
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void updateMetrics() {
        try {
            updateUserMetrics();
            updateTokenMetrics();
        } catch (Exception e) {
            // Log error but don't fail the application
            // The metrics will be updated on the next scheduled run
        }
    }
    
    private void updateUserMetrics() {
        try {
            // Update user counts
            totalUsers.set(userRepository.count());
            enabledUsers.set(userRepository.countByEnabled(true));
            disabledUsers.set(userRepository.countByEnabled(false));
            
            // Count admin users
            long adminCount = userRepository.countByRole(co.com.gedsys.authentication.entity.Role.ADMIN);
            adminUsers.set(adminCount);
            
        } catch (Exception e) {
            // Metrics update failed, but don't break the application
        }
    }
    
    private void updateTokenMetrics() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // Update refresh token metrics
            long activeTokens = refreshTokenRepository.countValidTokens(now);
            long expiredTokens = refreshTokenRepository.countExpiredTokens(now);
            
            activeRefreshTokens.set(activeTokens);
            expiredRefreshTokens.set(expiredTokens);
            
            // Update push token metrics
            long totalPushTokens = pushTokenRepository.count();
            activePushTokens.set(totalPushTokens);
            
            // Count stale push tokens (not used in 30 days)
            LocalDateTime staleDate = now.minusDays(30);
            long staleTokens = pushTokenRepository.countStaleTokens(staleDate);
            stalePushTokens.set(staleTokens);
            
        } catch (Exception e) {
            // Metrics update failed, but don't break the application
        }
    }
}