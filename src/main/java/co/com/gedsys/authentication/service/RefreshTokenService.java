package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing refresh tokens with single-use policy and session management.
 * Handles token generation, validation, single-use enforcement, and automatic cleanup.
 */
@Service
@Transactional
public class RefreshTokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(RefreshTokenService.class);
    private static final int TOKEN_LENGTH = 32;
    
    private final RefreshTokenRepository refreshTokenRepository;
    private final JwtProperties jwtProperties;
    private final SecureRandom secureRandom;
    
    public RefreshTokenService(RefreshTokenRepository refreshTokenRepository, JwtProperties jwtProperties) {
        this.refreshTokenRepository = refreshTokenRepository;
        this.jwtProperties = jwtProperties;
        this.secureRandom = new SecureRandom();
    }
    
    /**
     * Generate a new refresh token for a user and session type.
     * Invalidates any existing token for the same user and session type.
     * 
     * @param user the user to generate token for
     * @param sessionType the session type (MOBILE or WEB)
     * @return the generated refresh token
     */
    public RefreshToken generateRefreshToken(User user, SessionType sessionType) {
        logger.debug("SECURITY_EVENT: Refresh token generation - creating new refresh token for user: {} with session type: {}", 
                    user.getId(), sessionType);
        
        // Invalidate existing token for this user and session type
        invalidateExistingToken(user, sessionType);
        
        // Generate new token
        String tokenValue = generateSecureToken();
        LocalDateTime expiryDate = LocalDateTime.now().plusSeconds(jwtProperties.getRefreshTokenExpiration() / 1000);
        
        RefreshToken refreshToken = new RefreshToken(tokenValue, user, sessionType, expiryDate);
        RefreshToken savedToken = refreshTokenRepository.save(refreshToken);
        
        logger.info("SECURITY_EVENT: Refresh token created - new refresh token generated for user: {} with session type: {} (expires: {})", 
                   user.getId(), sessionType, expiryDate);
        return savedToken;
    }
    
    /**
     * Validate and consume a refresh token (single-use policy).
     * Marks the token as used after validation.
     * 
     * @param tokenValue the token string to validate
     * @return the refresh token if valid
     * @throws IllegalArgumentException if token is invalid, expired, or already used
     */
    public RefreshToken validateAndConsumeToken(String tokenValue) {
        logger.debug("SECURITY_EVENT: Refresh token validation - attempting to validate and consume refresh token");
        
        LocalDateTime currentTime = LocalDateTime.now();
        Optional<RefreshToken> tokenOpt = refreshTokenRepository.findValidTokenByToken(tokenValue, currentTime);
        
        if (tokenOpt.isEmpty()) {
            logger.warn("SECURITY_EVENT: Refresh token validation failed - invalid or expired refresh token provided");
            throw new IllegalArgumentException("Invalid or expired refresh token");
        }
        
        RefreshToken refreshToken = tokenOpt.get();

        // Delete token physically (single-use policy)
        // This ensures the token cannot be reused and prevents accumulation of used tokens
        refreshTokenRepository.delete(refreshToken);

        logger.info("SECURITY_EVENT: Refresh token consumed - token validated and deleted for user: {} with session type: {}",
                   refreshToken.getUser().getId(), refreshToken.getSessionType());
        
        return refreshToken;
    }
    
    /**
     * Find a refresh token by its value without consuming it.
     * 
     * @param tokenValue the token string to find
     * @return Optional containing the refresh token if found
     */
    @Transactional(readOnly = true)
    public Optional<RefreshToken> findByToken(String tokenValue) {
        return refreshTokenRepository.findByToken(tokenValue);
    }
    
    /**
     * Check if a token is valid (not used and not expired).
     * 
     * @param tokenValue the token string to check
     * @return true if token is valid, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean isTokenValid(String tokenValue) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.findValidTokenByToken(tokenValue, currentTime).isPresent();
    }
    
    /**
     * Get all valid tokens for a user.
     * Only administrators can access user token information.
     * 
     * @param user the user to get tokens for
     * @return list of valid refresh tokens
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public List<RefreshToken> getValidTokensForUser(User user) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.findValidTokensByUser(user, currentTime);
    }
    
    /**
     * Check if user has a valid token for a specific session type.
     * 
     * @param user the user to check
     * @param sessionType the session type to check
     * @return true if user has valid token for session type
     */
    @Transactional(readOnly = true)
    public boolean hasValidTokenForSessionType(User user, SessionType sessionType) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.hasValidTokenForSessionType(user, sessionType, currentTime);
    }
    
    /**
     * Invalidate all tokens for a user.
     * Used during logout or when user account is disabled.
     * 
     * @param user the user whose tokens should be invalidated
     */
    public void invalidateAllUserTokens(User user) {
        logger.debug("SECURITY_EVENT: Token invalidation - invalidating all tokens for user: {}", user.getId());
        
        List<RefreshToken> userTokens = refreshTokenRepository.findByUser(user);
        userTokens.forEach(token -> {
            token.markAsUsed();
            refreshTokenRepository.save(token);
        });
        
        logger.info("SECURITY_EVENT: All tokens invalidated - {} refresh tokens invalidated for user: {}", 
                   userTokens.size(), user.getId());
    }
    
    /**
     * Invalidate tokens for a specific user and session type.
     * Used when user logs out or logs in on a new device of the same type.
     * Physically deletes tokens to enforce single session policy.
     *
     * @param user the user whose tokens should be invalidated
     * @param sessionType the session type to invalidate
     */
    public void invalidateTokensForSessionType(User user, SessionType sessionType) {
        logger.debug("SECURITY_EVENT: Session token invalidation - deleting {} tokens for user: {}",
                    sessionType, user.getId());

        refreshTokenRepository.deleteByUserAndSessionType(user, sessionType);

        logger.info("SECURITY_EVENT: Session tokens invalidated - {} session tokens deleted for user: {}",
                   sessionType, user.getId());
    }
    
    /**
     * Delete a specific refresh token.
     * 
     * @param refreshToken the token to delete
     */
    public void deleteToken(RefreshToken refreshToken) {
        logger.debug("Deleting refresh token for user {} with session type {}", 
                    refreshToken.getUser().getId(), refreshToken.getSessionType());
        
        refreshTokenRepository.delete(refreshToken);
        
        logger.info("Refresh token deleted for user {} with session type {}", 
                   refreshToken.getUser().getId(), refreshToken.getSessionType());
    }
    
    /**
     * Delete all tokens for a user.
     * Used during user deletion.
     * 
     * @param user the user whose tokens should be deleted
     */
    public void deleteAllUserTokens(User user) {
        logger.debug("Deleting all tokens for user {}", user.getId());
        
        refreshTokenRepository.deleteByUser(user);
        
        logger.info("All tokens deleted for user {}", user.getId());
    }
    
    /**
     * Delete tokens for a specific user and session type.
     * 
     * @param user the user whose tokens should be deleted
     * @param sessionType the session type to delete
     */
    public void deleteTokensForSessionType(User user, SessionType sessionType) {
        logger.debug("Deleting tokens for user {} with session type {}", user.getId(), sessionType);
        
        refreshTokenRepository.deleteByUserAndSessionType(user, sessionType);
        
        logger.info("Tokens deleted for user {} with session type {}", user.getId(), sessionType);
    }
    
    /**
     * Cleanup expired and used tokens.
     * This method is scheduled to run periodically.
     */
    @Scheduled(fixedRate = 3600000) // Run every hour
    public void cleanupExpiredAndUsedTokens() {
        logger.debug("SECURITY_EVENT: Token cleanup started - scanning for expired and used refresh tokens");
        
        LocalDateTime currentTime = LocalDateTime.now();
        
        // Delete expired tokens
        int expiredCount = refreshTokenRepository.deleteExpiredTokens(currentTime);
        if (expiredCount > 0) {
            logger.info("SECURITY_EVENT: Expired tokens cleaned - deleted {} expired refresh tokens", expiredCount);
        }
        
        // Delete used tokens
        int usedCount = refreshTokenRepository.deleteUsedTokens();
        if (usedCount > 0) {
            logger.info("SECURITY_EVENT: Used tokens cleaned - deleted {} used refresh tokens", usedCount);
        }
        
        if (expiredCount > 0 || usedCount > 0) {
            logger.info("SECURITY_EVENT: Token cleanup completed - deleted {} expired and {} used tokens", 
                       expiredCount, usedCount);
        } else {
            logger.debug("SECURITY_EVENT: Token cleanup completed - no tokens required cleanup");
        }
    }
    
    /**
     * Get count of valid tokens for a user.
     * 
     * @param user the user to count tokens for
     * @return count of valid tokens
     */
    @Transactional(readOnly = true)
    public long getValidTokenCountForUser(User user) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.countValidTokensByUser(user, currentTime);
    }
    
    /**
     * Generate a secure random token string.
     * 
     * @return base64 encoded secure random token
     */
    private String generateSecureToken() {
        byte[] tokenBytes = new byte[TOKEN_LENGTH];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }
    
    /**
     * Invalidate existing token for user and session type.
     * This enforces the single session policy per session type.
     * Physically deletes the token to prevent duplicate key constraint violations.
     *
     * @param user the user
     * @param sessionType the session type
     */
    private void invalidateExistingToken(User user, SessionType sessionType) {
        // Delete existing token physically to enforce single session policy
        // This prevents duplicate key constraint violations when creating new tokens
        refreshTokenRepository.deleteByUserAndSessionType(user, sessionType);

        logger.debug("SECURITY_EVENT: Existing token deleted - {} token deleted for user: {} due to new token generation",
                    sessionType, user.getId());
    }
}