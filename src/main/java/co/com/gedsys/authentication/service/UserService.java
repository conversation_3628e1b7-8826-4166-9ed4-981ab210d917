package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.dto.PagedResponse;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.exception.EmailAlreadyExistsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import co.com.gedsys.authentication.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service class for user management operations.
 * Provides CRUD operations, password encoding, and user validation.
 */
@Service
@Transactional
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }
    
    /**
     * Create a new user with encrypted password.
     * Only administrators can create new users.
     * 
     * @param user the user to create
     * @return the created user
     * @throws EmailAlreadyExistsException if email already exists
     */
    @PreAuthorize("hasRole('ADMIN')")
    public User createUser(User user) {
        logger.debug("Creating new user with email: {}", user.getEmail());
        
        // Check if email already exists
        if (userRepository.existsByEmail(user.getEmail())) {
            logger.warn("Attempt to create user with existing email: {}", user.getEmail());
            throw new EmailAlreadyExistsException(user.getEmail());
        }
        
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // Set default role if not specified
        if (user.getRole() == null) {
            user.setRole(Role.USER);
        }
        
        User savedUser = userRepository.save(user);
        logger.info("User created successfully with ID: {} and email: {}", savedUser.getId(), savedUser.getEmail());
        
        return savedUser;
    }
    
    /**
     * Update an existing user.
     * Only administrators can update user information.
     * 
     * @param userId the ID of the user to update
     * @param updatedUser the updated user data
     * @return the updated user
     * @throws UserNotFoundException if user not found
     * @throws EmailAlreadyExistsException if email already exists for another user
     */
    @PreAuthorize("hasRole('ADMIN')")
    public User updateUser(Long userId, User updatedUser) {
        logger.debug("Updating user with ID: {}", userId);
        
        User existingUser = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));
        
        // Check if email is being changed and if it already exists
        if (!existingUser.getEmail().equals(updatedUser.getEmail()) && 
            userRepository.existsByEmail(updatedUser.getEmail())) {
            logger.warn("Attempt to update user {} with existing email: {}", userId, updatedUser.getEmail());
            throw new EmailAlreadyExistsException(updatedUser.getEmail());
        }
        
        // Update fields
        existingUser.setEmail(updatedUser.getEmail());
        existingUser.setFirstName(updatedUser.getFirstName());
        existingUser.setLastName(updatedUser.getLastName());
        existingUser.setRole(updatedUser.getRole());
        existingUser.setEnabled(updatedUser.isEnabled());
        
        // Only update password if provided
        if (updatedUser.getPassword() != null && !updatedUser.getPassword().isEmpty()) {
            existingUser.setPassword(passwordEncoder.encode(updatedUser.getPassword()));
        }
        
        User savedUser = userRepository.save(existingUser);
        logger.info("User updated successfully with ID: {}", savedUser.getId());
        
        return savedUser;
    }
    
    /**
     * Delete a user by ID.
     * Only administrators can delete users.
     * 
     * @param userId the ID of the user to delete
     * @throws UserNotFoundException if user not found
     */
    @PreAuthorize("hasRole('ADMIN')")
    public void deleteUser(Long userId) {
        logger.debug("Deleting user with ID: {}", userId);
        
        if (!userRepository.existsById(userId)) {
            throw new UserNotFoundException(userId);
        }
        
        userRepository.deleteById(userId);
        logger.info("User deleted successfully with ID: {}", userId);
    }
    
    /**
     * Find a user by ID.
     * 
     * @param userId the user ID
     * @return the user
     * @throws UserNotFoundException if user not found
     */
    @Transactional(readOnly = true)
    public User findById(Long userId) {
        logger.debug("Finding user by ID: {}", userId);
        
        return userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));
    }
    
    /**
     * Find a user by email.
     * 
     * @param email the email address
     * @return Optional containing the user if found
     */
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        logger.debug("Finding user by email: {}", email);
        
        return userRepository.findByEmail(email);
    }
    
    /**
     * Get user profile response by ID.
     * 
     * @param userId the user ID
     * @return the user profile response
     * @throws UserNotFoundException if user not found
     */
    @Transactional(readOnly = true)
    public UserProfileResponse getUserProfile(Long userId) {
        logger.debug("Getting user profile for ID: {}", userId);
        
        User user = findById(userId);
        return new UserProfileResponse(user);
    }
    
    /**
     * Get paginated list of users with optional filters.
     * Only administrators can access user listings.
     * 
     * @param email email filter (partial match)
     * @param enabled enabled status filter
     * @param role role filter
     * @param pageable pagination parameters
     * @return paginated response of user profiles
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public PagedResponse<UserProfileResponse> getUsers(String email, Boolean enabled, Role role, Pageable pageable) {
        logger.debug("Getting users with filters - email: {}, enabled: {}, role: {}, page: {}", 
                    email, enabled, role, pageable.getPageNumber());
        
        Page<User> userPage = userRepository.findUsersWithFilters(email, enabled, role, pageable);
        
        Page<UserProfileResponse> responsePage = userPage.map(UserProfileResponse::new);
        
        return PagedResponse.of(responsePage);
    }
    
    /**
     * Get all users with pagination.
     * Only administrators can access user listings.
     * 
     * @param pageable pagination parameters
     * @return paginated response of user profiles
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public PagedResponse<UserProfileResponse> getAllUsers(Pageable pageable) {
        logger.debug("Getting all users with pagination - page: {}", pageable.getPageNumber());
        
        Page<User> userPage = userRepository.findAll(pageable);
        Page<UserProfileResponse> responsePage = userPage.map(UserProfileResponse::new);
        
        return PagedResponse.of(responsePage);
    }
    
    /**
     * Validate user credentials.
     * 
     * @param email the email address
     * @param rawPassword the raw password
     * @return the user if credentials are valid
     * @throws UserNotFoundException if user not found or credentials invalid
     */
    @Transactional(readOnly = true)
    public User validateUserCredentials(String email, String rawPassword) {
        logger.debug("SECURITY_EVENT: Credential validation - validating credentials for email: {}", email);
        
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> {
                    logger.warn("SECURITY_EVENT: Authentication failed - user not found for email: {}", email);
                    return new UserNotFoundException("email", email);
                });
        
        if (!user.isEnabled()) {
            logger.warn("SECURITY_EVENT: Authentication failed - login attempt for disabled user: {} (ID: {})", 
                       email, user.getId());
            throw new UserNotFoundException("User account is disabled");
        }
        
        if (!passwordEncoder.matches(rawPassword, user.getPassword())) {
            logger.warn("SECURITY_EVENT: Authentication failed - invalid password attempt for user: {} (ID: {})", 
                       email, user.getId());
            throw new UserNotFoundException("Invalid credentials");
        }
        
        logger.debug("SECURITY_EVENT: Credential validation successful - credentials validated for user: {} (ID: {})", 
                    email, user.getId());
        return user;
    }
    
    /**
     * Change user password.
     * 
     * @param userId the user ID
     * @param currentPassword the current password
     * @param newPassword the new password
     * @throws UserNotFoundException if user not found or current password is invalid
     */
    public void changePassword(Long userId, String currentPassword, String newPassword) {
        logger.debug("SECURITY_EVENT: Password change attempt - user: {} requesting password change", userId);
        
        User user = findById(userId);
        
        // Validate current password
        if (!passwordEncoder.matches(currentPassword, user.getPassword())) {
            logger.warn("SECURITY_EVENT: Password change failed - invalid current password for user: {} (email: {})", 
                       userId, user.getEmail());
            throw new UserNotFoundException("Current password is incorrect");
        }
        
        // Validate new password is different
        if (passwordEncoder.matches(newPassword, user.getPassword())) {
            logger.warn("SECURITY_EVENT: Password change failed - new password same as current for user: {} (email: {})", 
                       userId, user.getEmail());
            throw new IllegalArgumentException("New password must be different from current password");
        }
        
        // Update password
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        logger.info("SECURITY_EVENT: Password changed - password successfully changed for user: {} (email: {})", 
                   userId, user.getEmail());
    }
    
    /**
     * Check if email exists.
     * 
     * @param email the email to check
     * @return true if email exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }
    
    /**
     * Get user count by enabled status.
     * Only administrators can access user statistics.
     * 
     * @param enabled the enabled status
     * @return count of users
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public long getUserCountByEnabled(boolean enabled) {
        return userRepository.countByEnabled(enabled);
    }
    
    /**
     * Get user count by role.
     * Only administrators can access user statistics.
     * 
     * @param role the role
     * @return count of users
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public long getUserCountByRole(Role role) {
        return userRepository.countByRole(role);
    }
}