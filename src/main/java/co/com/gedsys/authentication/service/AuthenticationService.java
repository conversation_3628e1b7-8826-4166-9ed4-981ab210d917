package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.*;
import co.com.gedsys.authentication.exception.InvalidCredentialsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service that orchestrates all authentication operations.
 * Coordinates UserService, JwtService, RefreshTokenService, and PushTokenService.
 */
@Service
@Transactional
public class AuthenticationService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationService.class);
    
    private final UserService userService;
    private final JwtService jwtService;
    private final RefreshTokenService refreshTokenService;
    private final PushTokenService pushTokenService;
    private final JwtProperties jwtProperties;
    
    public AuthenticationService(UserService userService, 
                               JwtService jwtService,
                               RefreshTokenService refreshTokenService,
                               PushTokenService pushTokenService,
                               JwtProperties jwtProperties) {
        this.userService = userService;
        this.jwtService = jwtService;
        this.refreshTokenService = refreshTokenService;
        this.pushTokenService = pushTokenService;
        this.jwtProperties = jwtProperties;
    }
    
    /**
     * Authenticate user with credentials and session type handling.
     * Handles push token registration for mobile sessions.
     * 
     * @param loginRequest the login request containing credentials and session info
     * @return authentication response with tokens and user info
     * @throws InvalidCredentialsException if credentials are invalid
     */
    public AuthResponse login(LoginRequest loginRequest) {
        logger.info("SECURITY_EVENT: Authentication attempt for email: {} with session type: {} from device: {}", 
                   loginRequest.getEmail(), loginRequest.getSessionType(), 
                   loginRequest.getDeviceType() != null ? loginRequest.getDeviceType() : "UNKNOWN");
        
        try {
            // Validate user credentials
            User user = userService.validateUserCredentials(loginRequest.getEmail(), loginRequest.getPassword());
            
            // Log session invalidation event
            boolean hadExistingSession = refreshTokenService.hasValidTokenForSessionType(user, loginRequest.getSessionType());
            if (hadExistingSession) {
                logger.info("SECURITY_EVENT: Session invalidation - existing {} session invalidated for user: {} due to new login", 
                           loginRequest.getSessionType(), user.getId());
            }
            
            // Invalidate existing sessions of the same type (single session policy)
            refreshTokenService.invalidateTokensForSessionType(user, loginRequest.getSessionType());
            
            // Handle push token for mobile sessions
            if (loginRequest.isMobileSession() && loginRequest.hasPushTokenInfo()) {
                handlePushTokenRegistration(user, loginRequest);
            }
            
            // Generate JWT access token
            String accessToken = jwtService.generateAccessToken(user, loginRequest.getSessionType());
            
            // Generate and save refresh token
            RefreshToken refreshToken = refreshTokenService.generateRefreshToken(user, loginRequest.getSessionType());
            
            // Calculate token expiration time
            long expiresIn = getAccessTokenExpirationForSessionType(loginRequest.getSessionType()) / 1000;
            
            // Create response
            UserProfileResponse userProfile = new UserProfileResponse(user);
            AuthResponse response = new AuthResponse(accessToken, refreshToken.getToken(), expiresIn, userProfile);
            
            logger.info("SECURITY_EVENT: Authentication successful - user: {} logged in with session type: {} - session created", 
                       user.getId(), loginRequest.getSessionType());
            
            return response;
            
        } catch (UserNotFoundException e) {
            logger.warn("SECURITY_EVENT: Authentication failed - invalid credentials for email: {} - {}", 
                       loginRequest.getEmail(), e.getMessage());
            throw new InvalidCredentialsException("Invalid email or password");
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Authentication error - unexpected failure for email: {} - {}", 
                        loginRequest.getEmail(), e.getMessage(), e);
            throw new InvalidCredentialsException("Authentication failed");
        }
    }
    
    /**
     * Refresh JWT access token using refresh token with single-use enforcement.
     * 
     * @param refreshTokenValue the refresh token value
     * @return new authentication response with fresh tokens
     * @throws InvalidCredentialsException if refresh token is invalid
     */
    public AuthResponse refreshToken(String refreshTokenValue) {
        logger.debug("SECURITY_EVENT: Token refresh attempt");
        
        try {
            // Validate and consume refresh token (single-use policy)
            RefreshToken refreshToken = refreshTokenService.validateAndConsumeToken(refreshTokenValue);
            User user = refreshToken.getUser();
            SessionType sessionType = refreshToken.getSessionType();
            
            // Generate new JWT access token
            String newAccessToken = jwtService.generateAccessToken(user, sessionType);
            
            // Generate new refresh token
            RefreshToken newRefreshToken = refreshTokenService.generateRefreshToken(user, sessionType);
            
            // Calculate token expiration time
            long expiresIn = getAccessTokenExpirationForSessionType(sessionType) / 1000;
            
            // Create response
            UserProfileResponse userProfile = new UserProfileResponse(user);
            AuthResponse response = new AuthResponse(newAccessToken, newRefreshToken.getToken(), expiresIn, userProfile);
            
            logger.info("SECURITY_EVENT: Token refresh successful - user: {} refreshed {} session tokens", 
                       user.getId(), sessionType);
            
            return response;
            
        } catch (IllegalArgumentException e) {
            logger.warn("SECURITY_EVENT: Token refresh failed - invalid or expired refresh token: {}", e.getMessage());
            throw new InvalidCredentialsException("Invalid or expired refresh token");
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Token refresh error - unexpected failure: {}", e.getMessage(), e);
            throw new InvalidCredentialsException("Token refresh failed");
        }
    }
    
    /**
     * Logout user with proper token cleanup.
     * Removes refresh tokens and push tokens based on session type.
     * 
     * @param user the user to logout
     * @param sessionType the session type to logout from
     */
    public void logout(User user, SessionType sessionType) {
        logger.info("SECURITY_EVENT: Logout request - user: {} initiating logout from session type: {}", 
                   user.getId(), sessionType);
        
        try {
            // Invalidate refresh tokens for this session type
            refreshTokenService.invalidateTokensForSessionType(user, sessionType);
            
            // Remove push token if logging out from mobile
            if (SessionType.MOBILE.equals(sessionType)) {
                boolean pushTokenRemoved = pushTokenService.removePushToken(user);
                if (pushTokenRemoved) {
                    logger.info("SECURITY_EVENT: Push token cleanup - push token removed for user: {} during mobile logout", 
                               user.getId());
                }
            }
            
            logger.info("SECURITY_EVENT: Logout successful - user: {} logged out from session type: {} - session invalidated", 
                       user.getId(), sessionType);
            
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Logout error - failed to complete logout for user: {} with session type: {} - {}", 
                        user.getId(), sessionType, e.getMessage(), e);
            // Don't throw exception for logout - log and continue
        }
    }
    
    /**
     * Logout user from all sessions.
     * Invalidates all refresh tokens and removes push token.
     * 
     * @param user the user to logout from all sessions
     */
    public void logoutFromAllSessions(User user) {
        logger.info("SECURITY_EVENT: Global logout request - user: {} initiating logout from all sessions", user.getId());
        
        try {
            // Get session count before invalidation for logging
            long activeSessionCount = refreshTokenService.getValidTokenCountForUser(user);
            
            // Invalidate all refresh tokens
            refreshTokenService.invalidateAllUserTokens(user);
            
            // Remove push token
            boolean pushTokenRemoved = pushTokenService.removePushToken(user);
            if (pushTokenRemoved) {
                logger.info("SECURITY_EVENT: Push token cleanup - push token removed for user: {} during global logout", 
                           user.getId());
            }
            
            logger.info("SECURITY_EVENT: Global logout successful - user: {} logged out from all sessions ({} sessions invalidated)", 
                       user.getId(), activeSessionCount);
            
        } catch (Exception e) {
            logger.error("SECURITY_EVENT: Global logout error - failed to complete logout from all sessions for user: {} - {}", 
                        user.getId(), e.getMessage(), e);
            // Don't throw exception for logout - log and continue
        }
    }
    
    /**
     * Validate JWT token and return user information.
     * 
     * @param token the JWT token to validate
     * @return user profile if token is valid
     * @throws InvalidCredentialsException if token is invalid
     */
    @Transactional(readOnly = true)
    public UserProfileResponse validateToken(String token) {
        logger.debug("Token validation request");
        
        try {
            if (!jwtService.isTokenValid(token)) {
                throw new InvalidCredentialsException("Invalid token");
            }
            
            Long userId = jwtService.extractUserId(token);
            User user = userService.findById(userId);
            
            if (!jwtService.validateToken(token, user)) {
                throw new InvalidCredentialsException("Token validation failed");
            }
            
            logger.debug("Token validation successful for user: {}", userId);
            return new UserProfileResponse(user);
            
        } catch (UserNotFoundException e) {
            logger.warn("Token validation failed - user not found: {}", e.getMessage());
            throw new InvalidCredentialsException("Invalid token");
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            throw new InvalidCredentialsException("Invalid token");
        }
    }
    
    /**
     * Check if user has active session for a specific session type.
     * 
     * @param user the user to check
     * @param sessionType the session type to check
     * @return true if user has active session for the session type
     */
    @Transactional(readOnly = true)
    public boolean hasActiveSession(User user, SessionType sessionType) {
        return refreshTokenService.hasValidTokenForSessionType(user, sessionType);
    }
    
    /**
     * Get user's active sessions count.
     * 
     * @param user the user to check
     * @return count of active sessions
     */
    @Transactional(readOnly = true)
    public long getActiveSessionCount(User user) {
        return refreshTokenService.getValidTokenCountForUser(user);
    }
    
    /**
     * Handle push token registration for mobile sessions.
     * 
     * @param user the user to register push token for
     * @param loginRequest the login request containing push token info
     */
    private void handlePushTokenRegistration(User user, LoginRequest loginRequest) {
        try {
            // Validate device type for push tokens
            if (loginRequest.getDeviceType() != null) {
                pushTokenService.validateMobileDeviceType(loginRequest.getDeviceType());
            }
            
            // Check if user had existing push token
            boolean hadExistingPushToken = pushTokenService.hasPushToken(user);
            
            // Register or update push token
            pushTokenService.registerPushToken(
                user, 
                loginRequest.getPushToken(), 
                loginRequest.getDeviceType() != null ? loginRequest.getDeviceType() : DeviceType.ANDROID,
                loginRequest.getDeviceId()
            );
            
            if (hadExistingPushToken) {
                logger.info("SECURITY_EVENT: Push token replacement - existing push token replaced for user: {} with device type: {}", 
                           user.getId(), loginRequest.getDeviceType());
            } else {
                logger.info("SECURITY_EVENT: Push token registration - new push token registered for user: {} with device type: {}", 
                           user.getId(), loginRequest.getDeviceType());
            }
            
        } catch (Exception e) {
            logger.warn("SECURITY_EVENT: Push token registration failed - unable to register push token for user: {} - {}", 
                       user.getId(), e.getMessage());
            // Don't fail login if push token registration fails
        }
    }
    
    /**
     * Get access token expiration time for session type.
     * 
     * @param sessionType the session type
     * @return expiration time in milliseconds
     */
    private long getAccessTokenExpirationForSessionType(SessionType sessionType) {
        return switch (sessionType) {
            case MOBILE -> jwtProperties.getMobile().getAccessTokenExpiration();
            case WEB -> jwtProperties.getWeb().getAccessTokenExpiration();
        };
    }
}