package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.PushToken;
import co.com.gedsys.authentication.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for PushToken entity operations.
 * Provides methods for managing push notification tokens with one-to-one user relationship.
 */
@Repository
public interface PushTokenRepository extends JpaRepository<PushToken, Long> {
    
    /**
     * Find a push token by user.
     * Since there's a one-to-one relationship, each user can have at most one push token.
     * 
     * @param user the user to search for
     * @return Optional containing the push token if found, empty otherwise
     */
    Optional<PushToken> find<PERSON><PERSON><PERSON><PERSON>(User user);
    
    /**
     * Find a push token by the token string.
     * 
     * @param token the token string to search for
     * @return Optional containing the push token if found, empty otherwise
     */
    Optional<PushToken> findByToken(String token);
    
    /**
     * Find push tokens by device type.
     * 
     * @param deviceType the device type to filter by
     * @return List of push tokens with the specified device type
     */
    List<PushToken> findByDeviceType(DeviceType deviceType);
    
    /**
     * Find push tokens by device ID.
     * 
     * @param deviceId the device ID to search for
     * @return List of push tokens with the specified device ID
     */
    List<PushToken> findByDeviceId(String deviceId);
    
    /**
     * Find push tokens by device type and device ID.
     * 
     * @param deviceType the device type to filter by
     * @param deviceId the device ID to filter by
     * @return List of push tokens matching both criteria
     */
    List<PushToken> findByDeviceTypeAndDeviceId(DeviceType deviceType, String deviceId);
    
    /**
     * Check if a user has a push token.
     * 
     * @param user the user to check
     * @return true if the user has a push token, false otherwise
     */
    boolean existsByUser(User user);
    
    /**
     * Check if a push token exists with the given token string.
     * 
     * @param token the token string to check
     * @return true if a push token exists with the token, false otherwise
     */
    boolean existsByToken(String token);
    
    /**
     * Delete push token by user.
     * Used when a user logs out from mobile or when invalidating push notifications.
     * 
     * @param user the user whose push token should be deleted
     */
    void deleteByUser(User user);
    
    /**
     * Delete push token by token string.
     * 
     * @param token the token string to delete
     */
    void deleteByToken(String token);
    
    /**
     * Update the last used timestamp for a push token.
     * 
     * @param user the user whose push token should be updated
     * @param lastUsed the new last used timestamp
     * @return the number of updated records (should be 1 if successful)
     */
    @Modifying
    @Query("UPDATE PushToken pt SET pt.lastUsed = :lastUsed WHERE pt.user = :user")
    int updateLastUsedByUser(@Param("user") User user, @Param("lastUsed") LocalDateTime lastUsed);
    
    /**
     * Update the last used timestamp for a push token by token string.
     * 
     * @param token the token string to update
     * @param lastUsed the new last used timestamp
     * @return the number of updated records (should be 1 if successful)
     */
    @Modifying
    @Query("UPDATE PushToken pt SET pt.lastUsed = :lastUsed WHERE pt.token = :token")
    int updateLastUsedByToken(@Param("token") String token, @Param("lastUsed") LocalDateTime lastUsed);
    
    /**
     * Find push tokens that haven't been used since a specific date.
     * This can be used for cleanup operations to remove stale tokens.
     * 
     * @param cutoffDate the date to compare against
     * @return List of push tokens not used since the cutoff date
     */
    @Query("SELECT pt FROM PushToken pt WHERE pt.lastUsed IS NULL OR pt.lastUsed < :cutoffDate")
    List<PushToken> findStaleTokens(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * Delete push tokens that haven't been used since a specific date.
     * This is used for cleanup operations to remove old, unused tokens.
     * 
     * @param cutoffDate the date to compare against
     * @return the number of deleted tokens
     */
    @Modifying
    @Query("DELETE FROM PushToken pt WHERE pt.lastUsed IS NULL OR pt.lastUsed < :cutoffDate")
    int deleteStaleTokens(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * Count push tokens by device type.
     * 
     * @param deviceType the device type to count
     * @return the number of push tokens with the specified device type
     */
    long countByDeviceType(DeviceType deviceType);
    
    /**
     * Find push tokens created after a specific date.
     * 
     * @param date the date to compare against
     * @return List of push tokens created after the specified date
     */
    List<PushToken> findByCreatedAtAfter(LocalDateTime date);
    
    /**
     * Find push tokens created before a specific date.
     * 
     * @param date the date to compare against
     * @return List of push tokens created before the specified date
     */
    List<PushToken> findByCreatedAtBefore(LocalDateTime date);
    
    /**
     * Count push tokens that haven't been used since a specific date.
     * Used for metrics and monitoring.
     * 
     * @param cutoffDate the date to compare against
     * @return the number of stale push tokens
     */
    @Query("SELECT COUNT(pt) FROM PushToken pt WHERE pt.lastUsed IS NULL OR pt.lastUsed < :cutoffDate")
    long countStaleTokens(@Param("cutoffDate") LocalDateTime cutoffDate);
}