package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import co.com.gedsys.authentication.service.JwtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * Custom health check controller for monitoring application components.
 * Provides detailed health information for database, JWT service, and application metrics.
 */
@RestController
@RequestMapping("/health")
public class HealthController {
    
    private static final Logger logger = LoggerFactory.getLogger(HealthController.class);
    
    private final DataSource dataSource;
    private final JwtService jwtService;
    private final JwtProperties jwtProperties;
    private final UserRepository userRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PushTokenRepository pushTokenRepository;
    
    public HealthController(DataSource dataSource,
                           JwtService jwtService,
                           JwtProperties jwtProperties,
                           UserRepository userRepository,
                           RefreshTokenRepository refreshTokenRepository,
                           PushTokenRepository pushTokenRepository) {
        this.dataSource = dataSource;
        this.jwtService = jwtService;
        this.jwtProperties = jwtProperties;
        this.userRepository = userRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.pushTokenRepository = pushTokenRepository;
    }
    
    /**
     * Basic health check endpoint.
     * Available to all users for basic service status.
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // Check database connectivity
            boolean dbHealthy = checkDatabaseHealth();
            health.put("database", dbHealthy ? "UP" : "DOWN");
            
            // Check JWT configuration
            boolean jwtHealthy = checkJwtHealth();
            health.put("jwt", jwtHealthy ? "UP" : "DOWN");
            
            // Overall status
            boolean overallHealthy = dbHealthy && jwtHealthy;
            health.put("status", overallHealthy ? "UP" : "DOWN");
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            logger.debug("Health check completed - Database: {}, JWT: {}", dbHealthy, jwtHealthy);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            logger.error("Health check failed", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            return ResponseEntity.status(503).body(health);
        }
    }
    
    /**
     * Detailed health check endpoint with metrics.
     * Only available to administrators.
     */
    @GetMapping("/detailed")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getDetailedHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // Database health with details
            Map<String, Object> dbHealth = getDetailedDatabaseHealth();
            health.put("database", dbHealth);
            
            // JWT health with details
            Map<String, Object> jwtHealth = getDetailedJwtHealth();
            health.put("jwt", jwtHealth);
            
            // Application metrics
            Map<String, Object> metrics = getApplicationMetrics();
            health.put("metrics", metrics);
            
            // Overall status
            boolean dbHealthy = "UP".equals(dbHealth.get("status"));
            boolean jwtHealthy = "UP".equals(jwtHealth.get("status"));
            boolean overallHealthy = dbHealthy && jwtHealthy;
            
            health.put("status", overallHealthy ? "UP" : "DOWN");
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            logger.debug("Detailed health check completed");
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            logger.error("Detailed health check failed", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            return ResponseEntity.status(503).body(health);
        }
    }
    
    private boolean checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            return !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            logger.warn("Database health check failed", e);
            return false;
        }
    }
    
    private boolean checkJwtHealth() {
        try {
            // Check if JWT secret is configured
            String secret = jwtProperties.getSecret();
            if (secret == null || secret.trim().isEmpty()) {
                return false;
            }
            
            // Check if expiration times are valid
            long accessTokenExpiration = jwtProperties.getAccessTokenExpiration();
            long refreshTokenExpiration = jwtProperties.getRefreshTokenExpiration();
            
            return accessTokenExpiration > 0 && refreshTokenExpiration > 0;
            
        } catch (Exception e) {
            logger.warn("JWT health check failed", e);
            return false;
        }
    }
    
    private Map<String, Object> getDetailedDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isClosed() || !connection.isValid(5)) {
                dbHealth.put("status", "DOWN");
                dbHealth.put("error", "Database connection is not valid");
                return dbHealth;
            }
            
            dbHealth.put("status", "UP");
            dbHealth.put("database", connection.getMetaData().getDatabaseProductName());
            dbHealth.put("version", connection.getMetaData().getDatabaseProductVersion());
            
            // Check table existence
            dbHealth.put("tables", checkTablesExist(connection));
            
        } catch (SQLException e) {
            logger.warn("Detailed database health check failed", e);
            dbHealth.put("status", "DOWN");
            dbHealth.put("error", e.getMessage());
        }
        
        return dbHealth;
    }
    
    private Map<String, Object> getDetailedJwtHealth() {
        Map<String, Object> jwtHealth = new HashMap<>();
        
        try {
            // Check JWT configuration
            String secret = jwtProperties.getSecret();
            if (secret == null || secret.trim().isEmpty()) {
                jwtHealth.put("status", "DOWN");
                jwtHealth.put("error", "JWT secret not configured");
                return jwtHealth;
            }
            
            jwtHealth.put("status", "UP");
            jwtHealth.put("secret_configured", true);
            jwtHealth.put("secret_length", secret.length());
            jwtHealth.put("access_token_expiration_ms", jwtProperties.getAccessTokenExpiration());
            jwtHealth.put("refresh_token_expiration_ms", jwtProperties.getRefreshTokenExpiration());
            
            // Check mobile and web specific settings
            if (jwtProperties.getMobile() != null) {
                jwtHealth.put("mobile_access_token_expiration_ms", 
                             jwtProperties.getMobile().getAccessTokenExpiration());
            }
            
            if (jwtProperties.getWeb() != null) {
                jwtHealth.put("web_access_token_expiration_ms", 
                             jwtProperties.getWeb().getAccessTokenExpiration());
            }
            
        } catch (Exception e) {
            logger.warn("Detailed JWT health check failed", e);
            jwtHealth.put("status", "DOWN");
            jwtHealth.put("error", e.getMessage());
        }
        
        return jwtHealth;
    }
    
    private Map<String, Object> getApplicationMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // User metrics
            Map<String, Object> userMetrics = new HashMap<>();
            userMetrics.put("total", userRepository.count());
            userMetrics.put("enabled", userRepository.countByEnabled(true));
            userMetrics.put("disabled", userRepository.countByEnabled(false));
            userMetrics.put("admin", userRepository.countByRole(co.com.gedsys.authentication.entity.Role.ADMIN));
            metrics.put("users", userMetrics);
            
            // Token metrics
            Map<String, Object> tokenMetrics = new HashMap<>();
            tokenMetrics.put("refresh_active", refreshTokenRepository.countValidTokens(now));
            tokenMetrics.put("refresh_expired", refreshTokenRepository.countExpiredTokens(now));
            tokenMetrics.put("push_total", pushTokenRepository.count());
            
            LocalDateTime staleDate = now.minusDays(30);
            tokenMetrics.put("push_stale", pushTokenRepository.countStaleTokens(staleDate));
            metrics.put("tokens", tokenMetrics);
            
        } catch (Exception e) {
            logger.warn("Failed to collect application metrics", e);
            metrics.put("error", "Failed to collect metrics: " + e.getMessage());
        }
        
        return metrics;
    }
    
    private Map<String, Boolean> checkTablesExist(Connection connection) {
        Map<String, Boolean> tables = new HashMap<>();
        String[] tableNames = {"users", "refresh_tokens", "push_tokens"};
        
        for (String tableName : tableNames) {
            try {
                String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ? AND table_schema = 'public'";
                try (var stmt = connection.prepareStatement(sql)) {
                    stmt.setString(1, tableName);
                    try (var rs = stmt.executeQuery()) {
                        tables.put(tableName, rs.next() && rs.getInt(1) > 0);
                    }
                }
            } catch (SQLException e) {
                logger.warn("Failed to check table existence for {}", tableName, e);
                tables.put(tableName, false);
            }
        }
        
        return tables;
    }
}