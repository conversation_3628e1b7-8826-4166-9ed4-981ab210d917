package co.com.gedsys.authentication.controller;

import co.com.gedsys.authentication.dto.*;
import co.com.gedsys.authentication.entity.PushToken;
import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.service.AuthenticationService;
import co.com.gedsys.authentication.service.PushTokenService;
import co.com.gedsys.authentication.service.RefreshTokenService;
import co.com.gedsys.authentication.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for administrative user management operations.
 * Provides CRUD operations, user listing with pagination, and detailed user information.
 * All endpoints require ADMIN role.
 */
@RestController
@RequestMapping("/admin/users")
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "Administración", description = "Endpoints para gestión administrativa de usuarios (requiere rol ADMIN)")
@SecurityRequirement(name = "Bearer Authentication")
public class AdminController {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);
    
    private final UserService userService;
    private final RefreshTokenService refreshTokenService;
    private final PushTokenService pushTokenService;
    private final AuthenticationService authenticationService;
    
    public AdminController(UserService userService,
                          RefreshTokenService refreshTokenService,
                          PushTokenService pushTokenService,
                          AuthenticationService authenticationService) {
        this.userService = userService;
        this.refreshTokenService = refreshTokenService;
        this.pushTokenService = pushTokenService;
        this.authenticationService = authenticationService;
    }
    
    /**
     * Get paginated list of users with optional filters.
     * Supports filtering by email, enabled status, and role.
     * 
     * @param email email filter (partial match)
     * @param enabled enabled status filter
     * @param role role filter
     * @param page page number (default: 0)
     * @param size page size (default: 20)
     * @param sort sort criteria (default: "createdAt,desc")
     * @return paginated response of user profiles
     */
    @GetMapping
    public ResponseEntity<PagedResponse<UserProfileResponse>> getUsers(
            @RequestParam(required = false) String email,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) Role role,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort) {
        
        logger.debug("Admin user listing request - email: {}, enabled: {}, role: {}, page: {}, size: {}", 
                    email, enabled, role, page, size);
        
        try {
            // Parse sort parameter
            Pageable pageable = createPageable(page, size, sort);
            
            // Get users with filters
            PagedResponse<UserProfileResponse> response = userService.getUsers(email, enabled, role, pageable);
            
            logger.debug("Retrieved {} users for admin listing", response.getContent().size());
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid pagination parameters: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error retrieving users for admin", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * Get detailed user information by ID including sessions and push token.
     * Returns complete user information for administrative purposes.
     * 
     * @param userId the user ID
     * @return detailed admin user response
     */
    @GetMapping("/{userId}")
    public ResponseEntity<?> getUserById(@PathVariable Long userId) {
        logger.debug("Admin user detail request for user ID: {}", userId);
        
        try {
            // Get user
            User user = userService.findById(userId);
            
            // Get active sessions
            List<ActiveSessionInfo> activeSessions = getActiveSessionsForUser(user);
            
            // Get push token info
            PushTokenInfo pushTokenInfo = getPushTokenInfoForUser(user);
            
            // Create admin response
            AdminUserResponse response = new AdminUserResponse(user, activeSessions, pushTokenInfo);
            
            logger.debug("Retrieved detailed information for user ID: {}", userId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error retrieving user details for admin", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to retrieve user details"));
        }
    }
    
    /**
     * Create a new user.
     * 
     * @param createUserRequest the user creation request
     * @return the created user profile
     */
    @PostMapping
    public ResponseEntity<?> createUser(@Valid @RequestBody CreateUserRequest createUserRequest) {
        logger.debug("Admin user creation request for email: {}", createUserRequest.getEmail());
        
        try {
            // Create user entity
            User user = new User();
            user.setEmail(createUserRequest.getEmail());
            user.setPassword(createUserRequest.getPassword());
            user.setFirstName(createUserRequest.getFirstName());
            user.setLastName(createUserRequest.getLastName());
            user.setRole(createUserRequest.getRole() != null ? createUserRequest.getRole() : Role.USER);
            user.setEnabled(createUserRequest.isEnabled());
            
            // Create user
            User createdUser = userService.createUser(user);
            
            // Return user profile
            UserProfileResponse response = new UserProfileResponse(createdUser);
            
            logger.info("User created successfully by admin - ID: {}, email: {}", 
                       createdUser.getId(), createdUser.getEmail());
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (Exception e) {
            logger.error("Error creating user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create user"));
        }
    }
    
    /**
     * Update an existing user.
     * 
     * @param userId the user ID to update
     * @param updateUserRequest the user update request
     * @return the updated user profile
     */
    @PutMapping("/{userId}")
    public ResponseEntity<?> updateUser(@PathVariable Long userId, 
                                       @Valid @RequestBody UpdateUserRequest updateUserRequest) {
        logger.debug("Admin user update request for user ID: {}", userId);
        
        try {
            // Create user entity with updates
            User userUpdates = new User();
            userUpdates.setEmail(updateUserRequest.getEmail());
            userUpdates.setFirstName(updateUserRequest.getFirstName());
            userUpdates.setLastName(updateUserRequest.getLastName());
            userUpdates.setRole(updateUserRequest.getRole());
            userUpdates.setEnabled(updateUserRequest.isEnabled());
            
            // Set password only if provided
            if (updateUserRequest.getPassword() != null && !updateUserRequest.getPassword().trim().isEmpty()) {
                userUpdates.setPassword(updateUserRequest.getPassword());
            }
            
            // Update user
            User updatedUser = userService.updateUser(userId, userUpdates);
            
            // If password was changed, invalidate all sessions
            if (updateUserRequest.getPassword() != null && !updateUserRequest.getPassword().trim().isEmpty()) {
                authenticationService.logoutFromAllSessions(updatedUser);
                logger.info("All sessions invalidated for user {} due to password change by admin", userId);
            }
            
            // Return user profile
            UserProfileResponse response = new UserProfileResponse(updatedUser);
            
            logger.info("User updated successfully by admin - ID: {}", userId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error updating user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to update user"));
        }
    }
    
    /**
     * Delete a user by ID.
     * Invalidates all sessions and removes associated tokens.
     * 
     * @param userId the user ID to delete
     * @return success response
     */
    @DeleteMapping("/{userId}")
    public ResponseEntity<?> deleteUser(@PathVariable Long userId) {
        logger.debug("Admin user deletion request for user ID: {}", userId);
        
        try {
            // Get user first to invalidate sessions
            User user = userService.findById(userId);
            
            // Invalidate all sessions and remove tokens
            authenticationService.logoutFromAllSessions(user);
            
            // Delete user (cascade will handle related tokens)
            userService.deleteUser(userId);
            
            logger.info("User deleted successfully by admin - ID: {}", userId);
            return ResponseEntity.ok(Map.of("message", "User deleted successfully"));
            
        } catch (Exception e) {
            logger.error("Error deleting user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete user"));
        }
    }
    
    /**
     * Enable or disable a user.
     * 
     * @param userId the user ID
     * @param enabled the enabled status
     * @return success response
     */
    @PatchMapping("/{userId}/enabled")
    public ResponseEntity<?> setUserEnabled(@PathVariable Long userId, 
                                           @RequestBody Map<String, Boolean> request) {
        logger.debug("Admin user enable/disable request for user ID: {}", userId);
        
        try {
            Boolean enabled = request.get("enabled");
            if (enabled == null) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "Enabled status is required"));
            }
            
            // Get current user
            User user = userService.findById(userId);
            user.setEnabled(enabled);
            
            // Update user
            User updatedUser = userService.updateUser(userId, user);
            
            // If disabling user, invalidate all sessions
            if (!enabled) {
                authenticationService.logoutFromAllSessions(updatedUser);
                logger.info("All sessions invalidated for disabled user {}", userId);
            }
            
            logger.info("User {} status changed to {} by admin", userId, enabled ? "enabled" : "disabled");
            return ResponseEntity.ok(Map.of(
                    "message", "User " + (enabled ? "enabled" : "disabled") + " successfully",
                    "enabled", enabled
            ));
            
        } catch (Exception e) {
            logger.error("Error changing user enabled status", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to change user status"));
        }
    }
    
    /**
     * Invalidate all sessions for a user.
     * 
     * @param userId the user ID
     * @return success response
     */
    @PostMapping("/{userId}/invalidate-sessions")
    public ResponseEntity<?> invalidateUserSessions(@PathVariable Long userId) {
        logger.debug("Admin session invalidation request for user ID: {}", userId);
        
        try {
            User user = userService.findById(userId);
            authenticationService.logoutFromAllSessions(user);
            
            logger.info("All sessions invalidated for user {} by admin", userId);
            return ResponseEntity.ok(Map.of("message", "All user sessions invalidated successfully"));
            
        } catch (Exception e) {
            logger.error("Error invalidating user sessions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to invalidate user sessions"));
        }
    }
    
    /**
     * Get user statistics.
     * 
     * @return user statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<?> getUserStats() {
        logger.debug("Admin user statistics request");
        
        try {
            long totalUsers = userService.getUserCountByEnabled(true) + userService.getUserCountByEnabled(false);
            long enabledUsers = userService.getUserCountByEnabled(true);
            long disabledUsers = userService.getUserCountByEnabled(false);
            long adminUsers = userService.getUserCountByRole(Role.ADMIN);
            long regularUsers = userService.getUserCountByRole(Role.USER);
            long totalPushTokens = pushTokenService.getTotalTokenCount();
            
            Map<String, Object> stats = Map.of(
                    "totalUsers", totalUsers,
                    "enabledUsers", enabledUsers,
                    "disabledUsers", disabledUsers,
                    "adminUsers", adminUsers,
                    "regularUsers", regularUsers,
                    "totalPushTokens", totalPushTokens
            );
            
            logger.debug("Retrieved user statistics");
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            logger.error("Error retrieving user statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to retrieve user statistics"));
        }
    }
    
    /**
     * Get active sessions for a user.
     * 
     * @param user the user
     * @return list of active session information
     */
    private List<ActiveSessionInfo> getActiveSessionsForUser(User user) {
        List<ActiveSessionInfo> activeSessions = new ArrayList<>();
        
        try {
            List<RefreshToken> validTokens = refreshTokenService.getValidTokensForUser(user);
            
            for (RefreshToken token : validTokens) {
                ActiveSessionInfo sessionInfo = new ActiveSessionInfo(
                        token.getSessionType(),
                        token.getCreatedAt(), // Using creation time as last activity
                        token.getExpiryDate()
                );
                activeSessions.add(sessionInfo);
            }
            
        } catch (Exception e) {
            logger.warn("Error retrieving active sessions for user {}: {}", user.getId(), e.getMessage());
        }
        
        return activeSessions;
    }
    
    /**
     * Get push token information for a user.
     * 
     * @param user the user
     * @return push token information or null if not found
     */
    private PushTokenInfo getPushTokenInfoForUser(User user) {
        try {
            Optional<PushToken> pushTokenOpt = pushTokenService.getPushTokenByUser(user);
            return pushTokenOpt.map(PushTokenInfo::new).orElse(null);
        } catch (Exception e) {
            logger.warn("Error retrieving push token for user {}: {}", user.getId(), e.getMessage());
            return null;
        }
    }
    
    /**
     * Create Pageable object from request parameters.
     * 
     * @param page page number
     * @param size page size
     * @param sort sort criteria
     * @return Pageable object
     */
    private Pageable createPageable(int page, int size, String sort) {
        // Validate page and size
        if (page < 0) {
            throw new IllegalArgumentException("Page number cannot be negative");
        }
        if (size <= 0 || size > 100) {
            throw new IllegalArgumentException("Page size must be between 1 and 100");
        }
        
        // Parse sort parameter
        Sort sortObj = Sort.by(Sort.Direction.DESC, "createdAt"); // Default sort
        
        if (sort != null && !sort.trim().isEmpty()) {
            String[] sortParts = sort.split(",");
            if (sortParts.length == 2) {
                String property = sortParts[0].trim();
                String direction = sortParts[1].trim();
                
                Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) 
                        ? Sort.Direction.ASC 
                        : Sort.Direction.DESC;
                
                sortObj = Sort.by(sortDirection, property);
            } else if (sortParts.length == 1) {
                sortObj = Sort.by(Sort.Direction.ASC, sortParts[0].trim());
            }
        }
        
        return PageRequest.of(page, size, sortObj);
    }
}