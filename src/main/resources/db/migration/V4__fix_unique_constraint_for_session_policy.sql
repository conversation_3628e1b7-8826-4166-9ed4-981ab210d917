-- Fix unique constraint to support single session policy
-- This migration resolves the duplicate key error by allowing multiple records
-- per (user_id, session_type) but only one active (used=false) at a time

-- Drop the existing unique constraint
ALTER TABLE refresh_tokens DROP CONSTRAINT IF EXISTS uk_user_session_type;

-- Create a partial unique index that only applies to active tokens (used=false)
-- This allows multiple used tokens but only one active token per (user_id, session_type)
CREATE UNIQUE INDEX uk_user_session_type_active 
ON refresh_tokens (user_id, session_type) 
WHERE used = false;

-- Add comment for documentation
COMMENT ON INDEX uk_user_session_type_active IS 
'Ensures single active session policy: only one active refresh token per user per session type';
