-- Insert default admin user for system initialization
-- This migration creates a default administrator account for initial system access

-- Insert default admin user
-- Password: 'admin123' (BCrypt encoded)
-- Note: This password should be changed immediately after first login in production
INSERT INTO users (
    email,
    password,
    first_name,
    last_name,
    role,
    enabled,
    created_at,
    updated_at
) VALUES (
    '<EMAIL>',
    '$2a$10$RXY9gYyzn2qeW2AfB5eycez9wtXgJ0tJYMbf3mzrQ.cQaB8uMNTZu',
    'System',
    'Administrator',
    'ADMIN',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- Add comment for documentation
COMMENT ON TABLE users IS 'Default admin user created for system initialization. Password should be changed after first login.';